# 🚀 Market Tracking System - Deployment & Testing Guide

## 🏠 **Local Testing Options**

### Option 1: Node.js (Recommended for Corporate Devices)

Since you have Node.js installed, this is the easiest option:

```bash
# Navigate to project directory
cd c:\Users\<USER>\Desktop\market-tracking-system

# Install http-server globally (one time only)
npm install -g http-server

# Start the server
http-server -p 8000 -c-1 --cors
```

**Or use the batch file:**
```bash
# Double-click or run:
start-server-node.bat
```

**Then open:** `http://localhost:8000`

### Option 2: Alternative Node.js Servers

If `http-server` doesn't work, try these alternatives:

```bash
# Option A: Using npx (no global install needed)
npx http-server -p 8000 -c-1 --cors

# Option B: Using live-server
npm install -g live-server
live-server --port=8000 --cors

# Option C: Using serve
npm install -g serve
serve -s . -l 8000 --cors
```

## 🌐 **Netlify Deployment (Fixed)**

### What I Fixed:
1. **Supabase Loading Issue:** Added proper script loading order
2. **Module Loading:** Fixed ES6 module imports
3. **Headers:** Added proper content-type headers
4. **Configuration:** Added `netlify.toml` for proper routing

### Deploy to Netlify:

1. **Zip your project files** (or use Git)
2. **Go to Netlify.com** and drag/drop the zip file
3. **Or connect your GitHub repository**

### Files Added for Netlify:
- `netlify.toml` - Netlify configuration
- `_headers` - HTTP headers configuration
- Updated `index.html` - Proper Supabase script loading

## 📋 **Pre-Deployment Checklist**

### ✅ Required Steps:

1. **Update Supabase Config:**
   ```javascript
   // js/config.js
   export const SUPABASE_URL = 'https://nfrkmsbrxpttyqmeskue.supabase.co';
   export const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
   ```

2. **Run Database Scripts:**
   - ✅ Main schema: `schema_with_admin_fixed.sql`
   - ✅ Test data: `test_users.sql` (fixed version)

3. **Test Locally First:**
   - ✅ Start local server
   - ✅ Login as `admin/admin123`
   - ✅ Test basic functionality

## 🔑 **Test Accounts (After Running test_users.sql)**

### Admin
- **Username:** `admin`
- **Password:** `admin123`

### Senior Managers
- **Username:** `sm.mohamed` | **Password:** `sm123`
- **Username:** `sm.fatma` | **Password:** `sm123`

### District Managers
- **Username:** `dm.omar` | **Password:** `dm123`
- **Username:** `dm.nour` | **Password:** `dm123`
- **Username:** `dm.youssef` | **Password:** `dm123`

### Product Specialists
- **Username:** `ps.sara` | **Password:** `ps123`
- **Username:** `ps.amr` | **Password:** `ps123`
- **Username:** `ps.mona` | **Password:** `ps123`
- **Username:** `ps.karim` | **Password:** `ps123`
- **Username:** `ps.dina` | **Password:** `ps123`

## 🧪 **Testing Steps**

### 1. Local Testing
```bash
# Start server
npm install -g http-server
http-server -p 8000 -c-1 --cors

# Open browser
http://localhost:8000
```

### 2. Login Test
- Go to `http://localhost:8000`
- Should redirect to login page
- Login as `admin/admin123`
- Should redirect to admin dashboard

### 3. Functionality Test
- **Admin:** Create users, manage approvals
- **PS:** Add cases, doctors, accounts, products
- **Manager:** Approve team submissions
- **All:** View dashboards, tables, notifications

## 🔧 **Troubleshooting**

### ❌ "Cannot read properties of undefined (reading 'createClient')"
**Fixed!** The Supabase script now loads properly before modules.

### ❌ Blank Page
- ✅ Use local server (not file:// protocol)
- ✅ Check browser console for errors
- ✅ Verify Supabase credentials in `js/config.js`

### ❌ Login Fails
- ✅ Check Supabase credentials
- ✅ Verify test data was inserted
- ✅ Check browser network tab for API errors

### ❌ CORS Errors
- ✅ Use `--cors` flag with http-server
- ✅ Don't open HTML files directly

## 🎯 **Quick Start Commands**

### For Node.js Local Testing:
```bash
cd c:\Users\<USER>\Desktop\market-tracking-system
npm install -g http-server
http-server -p 8000 -c-1 --cors
```

### For Netlify Deployment:
1. Zip the entire project folder
2. Go to netlify.com
3. Drag and drop the zip file
4. Wait for deployment
5. Test with your Netlify URL

## ✅ **Success Criteria**

- ✅ Local server starts without errors
- ✅ Login page loads at `http://localhost:8000`
- ✅ Admin login works: `admin/admin123`
- ✅ Dashboard loads with navigation
- ✅ All role types can login and access their pages
- ✅ Netlify deployment shows login page (not blank)

## 🚀 **You're Ready!**

The application is now fixed for both local testing and Netlify deployment. The Supabase loading issue has been resolved, and you have multiple options for local testing with Node.js.

**Start with local testing first, then deploy to Netlify once everything works locally!**
