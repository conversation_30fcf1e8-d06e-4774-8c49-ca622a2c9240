# 🚀 Market Tracking System - Local Testing Guide

## ⚠️ IMPORTANT: How to Run Locally

### ❌ WRONG WAY (Causes CORS Errors)
- Opening `index.html` directly in browser
- Double-clicking HTML files
- Using `file://` protocol

### ✅ CORRECT WAY (Use Local Server)

**Option A: Using Python (Recommended)**
```bash
# Navigate to project directory
cd c:\Users\<USER>\Desktop\market-tracking-system

# Run the development server
python server.py

# Or double-click the batch file
start-server.bat
```

**Option B: Using Node.js**
```bash
npm install -g http-server
http-server -p 8000 --cors
```

**Option C: Using PHP**
```bash
php -S localhost:8000
```

### 🌐 Open in Browser
- Go to: `http://localhost:8000`
- **NOT** `file:///C:/path/to/index.html`

## 📋 Prerequisites Checklist

- ✅ Run `schema_with_admin_fixed.sql` in Supabase
- ✅ Run the **updated** `test_users.sql` in Supabase  
- ✅ Update `js/config.js` with your Supabase credentials
- ✅ Start local server (see above)
- ✅ Open `http://localhost:8000` in browser

## 🔑 Test Accounts

### Admin (Already exists from main schema)
- **Username:** `admin` | **Password:** `admin123`

### Senior Managers
- **Username:** `sm.mohamed` | **Password:** `sm123`
- **Username:** `sm.fatma` | **Password:** `sm123`

### District Managers  
- **Username:** `dm.omar` | **Password:** `dm123`
- **Username:** `dm.nour` | **Password:** `dm123`
- **Username:** `dm.youssef` | **Password:** `dm123`

### Product Specialists
- **Username:** `ps.sara` | **Password:** `ps123`
- **Username:** `ps.amr` | **Password:** `ps123`
- **Username:** `ps.mona` | **Password:** `ps123`
- **Username:** `ps.karim` | **Password:** `ps123`
- **Username:** `ps.dina` | **Password:** `ps123`

## 🧪 Testing Steps

### 1. Test Admin Functions
1. Login: `admin/admin123`
2. **Users Page:** Create new users, view hierarchy
3. **Approvals Page:** See all pending items system-wide
4. **Master Data:** Manage companies/categories
5. **Cases Page:** View all system cases
6. **Tables Page:** Browse all data
7. **Dashboard:** System metrics

### 2. Test Product Specialist
1. Login: `ps.sara/ps123`
2. **Add Case:** Create surgical case with products
3. **Add Doctor:** Add new doctor
4. **Add Account:** Add hospital/clinic
5. **Add Product:** Suggest new product
6. **Cases:** View your cases only
7. **Tables:** View approved data only
8. **Dashboard:** Personal metrics

### 3. Test Manager Approval
1. Login: `dm.omar/dm123` (Sara's manager)
2. **Approvals:** See Sara's submissions
3. **Approve/Reject:** Test with comments
4. **Dashboard:** Team metrics
5. **Cases:** Team cases view

### 4. Test Full Workflow
1. **PS creates** → `ps.sara` adds case
2. **DM approves** → `dm.omar` approves case  
3. **SM approves** → `sm.mohamed` approves case
4. **Admin approves** → `admin` final approval
5. **Verify** → Case appears in all relevant tables

## 🔧 Troubleshooting

### Blank Page / CORS Errors
- ✅ Use local server (`python server.py`)
- ❌ Don't open HTML files directly

### Login Fails
- ✅ Check `js/config.js` Supabase credentials
- ✅ Verify test data was inserted

### No Data
- ✅ Run `test_users.sql` in Supabase SQL Editor
- ✅ Check database has sample data

### Permission Errors  
- ✅ Verify user roles in database
- ✅ Check employee hierarchy is correct

## 🎯 Success Criteria

- ✅ All 4 roles can login and access pages
- ✅ Data visibility follows hierarchy rules
- ✅ Approval workflow works end-to-end
- ✅ Charts and tables load correctly
- ✅ Mobile responsive design works
- ✅ Forms validate and prevent duplicates

## 🚀 Ready to Test!

1. Start server: `python server.py`
2. Open: `http://localhost:8000`
3. Login as `admin/admin123`
4. Follow testing steps above

**The complete application with all 4 account types is ready for testing!** 🎉
