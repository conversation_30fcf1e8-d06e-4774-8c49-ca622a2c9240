# Market Tracking System

A comprehensive web application for tracking surgical cases, products used, and managing approvals across a sales hierarchy. Built with HTML, Bootstrap, vanilla JavaScript, and Supabase backend.

## Features

- **Role-based Access Control**: Product Specialists, District Managers, Senior Managers, and Administrators
- **Multi-step Approval Workflow**: Hierarchical approval system for cases, doctors, accounts, and products
- **Interactive Dashboards**: Chart.js-based analytics with filtering and export capabilities
- **Smart Tables**: Infinite scroll, column management, and Excel export functionality
- **Mobile-first Design**: Responsive UI with collapsible sidebar and modern gradients
- **Fuzzy Search**: Duplicate detection and auto-suggestions for data entry
- **Bulk Upload**: Excel-based import for doctors and accounts
- **Real-time Notifications**: In-app notification system with badge counts

## Technology Stack

- **Frontend**: HTML5, Bootstrap 5, Vanilla JavaScript (ES6 modules)
- **Backend**: Supabase (PostgreSQL database with REST API)
- **Charts**: Chart.js for analytics visualization
- **Styling**: Custom CSS with gradient themes and responsive design
- **Icons**: Font Awesome
- **Deployment**: Static hosting (GitHub Pages, Netlify, Vercel)

## Project Structure

```
/
├── index.html                  # Main entry point (redirects to login or dashboard)
├── login.html                  # Authentication page
├── css/
│   └── styles.css             # Custom styles with Bootstrap integration
├── js/
│   ├── config.js              # Supabase configuration
│   ├── config.example.js      # Configuration template
│   ├── auth.js                # Authentication module
│   ├── api.js                 # Supabase API wrapper
│   ├── layout.js              # Layout components and navigation
│   ├── utils.js               # Utility functions
│   ├── tables.js              # Smart table functionality
│   ├── charts.js              # Chart.js integration
│   ├── filters.js             # Filter components
│   ├── approvals.js           # Approval workflow
│   ├── fuzzy.js               # Fuzzy search utilities
│   └── bulk-upload.js         # Excel import functionality
├── ps/                        # Product Specialist pages
│   ├── dashboard.html
│   ├── add-case.html
│   ├── add-doctor.html
│   ├── add-account.html
│   ├── add-product.html
│   ├── cases.html
│   ├── tables.html
│   └── notifications.html
├── manager/                   # Manager (DM/SM) pages
│   ├── dashboard.html
│   ├── approvals.html
│   ├── add-case.html
│   ├── add-doctor.html
│   ├── add-account.html
│   ├── add-product.html
│   ├── cases.html
│   ├── tables.html
│   └── notifications.html
├── admin/                     # Administrator pages
│   ├── dashboard.html
│   ├── users.html
│   ├── approvals.html
│   ├── master-data.html
│   ├── bulk-upload.html
│   ├── cases.html
│   ├── tables.html
│   └── notifications.html
└── assets/                    # Logo and icon files
    ├── logo.svg               # Small header logo
    ├── main.svg               # Large login logo
    ├── main.ico               # Favicon
    ├── main_180.png           # Apple touch icon
    ├── main_192.png           # Android icon
    └── main_512.png           # High-res icon
```

## Setup Instructions

### 1. Database Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Run the SQL schema from `schema_with_admin_fixed.sql` in the Supabase SQL Editor
3. Note your project URL and anon key from the project settings

### 2. Configuration

1. Copy `js/config.example.js` to `js/config.js`
2. Update the configuration with your Supabase credentials:

```javascript
export const SUPABASE_URL = 'your-supabase-url';
export const SUPABASE_ANON_KEY = 'your-supabase-anon-key';
export const OUR_COMPANY_NAME = 'Your Company Name';
```

### 3. Assets Setup

Create the following logo and icon files in the `/assets` directory:

- `logo.svg` - Small header logo (recommended: 40px height)
- `main.svg` - Large login page logo (recommended: 300px max width)
- `main.ico` - Favicon (multi-size ICO file)
- `main_180.png` - Apple touch icon (180x180px)
- `main_192.png` - Android icon (192x192px)
- `main_512.png` - High-resolution icon (512x512px)

### 4. Local Development

For local development, you'll need to serve the files through a web server due to ES6 module restrictions:

```bash
# Using Python
python -m http.server 8000

# Using Node.js (http-server)
npx http-server

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

### 5. Deployment

The application is designed for static hosting. Deploy to:

- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **Netlify**: Drag and drop the project folder or connect to Git
- **Vercel**: Import the project from Git or deploy directly

## Default Login Credentials

The schema creates a default admin user:
- **Username**: `admin`
- **Password**: `admin123`

⚠️ **Important**: Change the default password after first login in production.

## User Roles and Permissions

### Product Specialist (PS)
- Add cases, doctors, accounts, and suggest products
- View own data and analytics
- Submit items for approval

### District Manager (DM) / Senior Manager (SM)
- All PS capabilities
- Approve direct reports' submissions
- View team data and analytics
- Manage team members

### Administrator
- Full system access
- Final approval authority
- User and master data management
- Bulk upload capabilities
- Global analytics and reporting

## Approval Workflow

1. **PS Submission** → DM Approval → Admin Final Approval
2. **DM Submission** → SM Approval → Admin Final Approval  
3. **SM Submission** → Admin Final Approval
4. **Admin Submission** → Auto-approved

## Key Features

### Smart Tables
- Infinite scroll pagination
- Column show/hide functionality
- Advanced filtering
- Excel export
- Sticky headers

### Analytics Dashboard
- Company market share analysis
- Product mix and category trends
- PS productivity metrics
- Coverage analysis
- Competitor incidence tracking

### Fuzzy Search
- Duplicate detection warnings
- Auto-suggestions for data entry
- Name similarity matching

### Mobile Responsiveness
- Collapsible sidebar navigation
- Touch-friendly interface
- Responsive tables and charts

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Security Notes

- Passwords are stored in plain text per business requirement
- No Row Level Security (RLS) - access control handled in application
- Client-side validation with server-side data integrity
- HTTPS required for production deployment

## Support

For technical support or questions about the system, contact your system administrator.

## License

This project is proprietary software developed for internal use.
