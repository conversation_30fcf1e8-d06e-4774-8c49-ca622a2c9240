# Market Tracking System - Testing Guide

## Quick Start Testing

### 1. Setup Test Data
First, run the test data SQL script in your Supabase SQL Editor:

```sql
-- Copy and paste the contents of test_users.sql into Supabase SQL Editor
-- This will create a complete hierarchy of test users and sample data
```

### 2. Test User Accounts

#### Administrator Account
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Administrator
- **Access**: Full system access, user management, final approvals

#### Senior Manager Accounts
- **Username**: `sm.mohamed` | **Password**: `sm123`
  - **Name**: <PERSON>
  - **Line**: Orthopedic | **Area**: North | **Location**: Cairo

- **Username**: `sm.fatma` | **Password**: `sm123`
  - **Name**: <PERSON><PERSON>
  - **Line**: Cardiac | **Area**: South | **Location**: Alexandria

#### District Manager Accounts
- **Username**: `dm.omar` | **Password**: `dm123`
  - **Name**: <PERSON>
  - **Line**: Orthopedic | **Area**: Cairo North | **Manager**: <PERSON>

- **Username**: `dm.nour` | **Password**: `dm123`
  - **Name**: Nour Ahmed
  - **Line**: Orthopedic | **Area**: Giza West | **Manager**: Mohamed Ali

- **Username**: `dm.youssef` | **Password**: `dm123`
  - **Name**: Youssef Ibrahim
  - **Line**: Cardiac | **Area**: Alex Central | **Manager**: Fatma Mahmoud

#### Product Specialist Accounts
- **Username**: `ps.sara` | **Password**: `ps123`
  - **Name**: Sara Mohamed
  - **Line**: Orthopedic | **Area**: Cairo Downtown | **Manager**: Omar Khaled

- **Username**: `ps.amr` | **Password**: `ps123`
  - **Name**: Amr Tarek
  - **Line**: Orthopedic | **Area**: Cairo East | **Manager**: Omar Khaled

- **Username**: `ps.mona` | **Password**: `ps123`
  - **Name**: Mona Sayed
  - **Line**: Orthopedic | **Area**: Giza Central | **Manager**: Nour Ahmed

- **Username**: `ps.karim` | **Password**: `ps123`
  - **Name**: Karim Farouk
  - **Line**: Cardiac | **Area**: Alex Marina | **Manager**: Youssef Ibrahim

- **Username**: `ps.dina` | **Password**: `ps123`
  - **Name**: Dina Osama
  - **Line**: Cardiac | **Area**: Alex Montaza | **Manager**: Youssef Ibrahim

## Testing Workflow

### Step 1: Start with Admin Account
1. Login as `admin` / `admin123`
2. Go to **User Management** to see the organizational hierarchy
3. Verify all users are created and active
4. Check the dashboard for system overview

### Step 2: Test Product Specialist Workflow
1. Login as `ps.sara` / `ps123`
2. **Dashboard**: View your analytics and recent activity
3. **Add Case**: 
   - Select a doctor (e.g., Dr. Ahmed Mostafa)
   - Select an account (e.g., Cairo University Hospital)
   - Add products from different companies
   - Submit for approval
4. **Add Doctor**: Create a new doctor with account associations
5. **Add Account**: Create a new hospital/clinic
6. **Add Product**: Suggest a new product for a company
7. **Cases**: View your submitted cases
8. **Tables**: Browse doctors, accounts, products, companies
9. **Notifications**: Check approval status updates

### Step 3: Test Manager Approval Workflow
1. Login as `dm.omar` / `dm123` (District Manager)
2. Check **Approvals** section for pending PS submissions
3. Approve or reject submissions from your team members
4. View team analytics and performance

### Step 4: Test Senior Manager Workflow
1. Login as `sm.mohamed` / `sm123` (Senior Manager)
2. Review approvals from District Managers
3. View broader team analytics
4. Make final approvals for complex cases

### Step 5: Test Admin Final Approvals
1. Login as `admin` / `admin123`
2. Review all pending approvals across the system
3. Make final approvals for master data (doctors, accounts, products)
4. Manage user accounts and system settings

## Key Features to Test

### Authentication & Authorization
- [ ] Login with different user roles
- [ ] Role-based page access restrictions
- [ ] Session management and logout
- [ ] Password validation

### Data Entry & Validation
- [ ] Case submission with multiple products
- [ ] Doctor creation with duplicate detection
- [ ] Account creation with uniqueness validation
- [ ] Product suggestion with company/category selection
- [ ] Form validation and error handling

### Approval Workflow
- [ ] Hierarchical approval process (PS → DM → SM → Admin)
- [ ] Status tracking (pending → approved/rejected)
- [ ] Comments and feedback system
- [ ] Email notifications (if configured)

### Data Visualization
- [ ] Dashboard charts and metrics
- [ ] Company market share analysis
- [ ] Trend analysis over time periods
- [ ] Export functionality for charts

### Smart Tables
- [ ] Infinite scroll and pagination
- [ ] Column sorting and filtering
- [ ] Search functionality
- [ ] Excel export
- [ ] Column visibility toggle

### Responsive Design
- [ ] Mobile device compatibility
- [ ] Tablet view optimization
- [ ] Desktop full-screen experience
- [ ] Collapsible sidebar navigation

## Sample Test Data Included

### Companies (8 companies)
- Al-Riyadah Medical, Johnson & Johnson, Medtronic, Stryker, etc.

### Categories (6 categories)
- Orthopedic Implants, Cardiac Devices, Surgical Instruments, etc.

### Products (12 approved products)
- Hip Replacement Systems, Pacemakers, Surgical Tools, etc.

### Accounts (10 hospitals/clinics)
- Mix of Private and UPA accounts across Cairo, Alexandria, Giza

### Doctors (8 doctors)
- Specialists in Orthopedic Surgery, Cardiac Surgery, General Surgery

### Sample Cases (5 cases)
- Mix of approved and pending cases with product associations

## Troubleshooting

### Common Issues
1. **Login fails**: Check username/password, verify user is active in database
2. **Pages don't load**: Check browser console for JavaScript errors
3. **Data not showing**: Verify Supabase connection and API keys
4. **Charts not rendering**: Check Chart.js library loading
5. **Export not working**: Verify XLSX library is loaded

### Database Verification
Run these queries in Supabase to verify test data:

```sql
-- Check users
SELECT u.username, u.role, e.full_name, e.position_title 
FROM users u 
JOIN employees e ON u.employee_id = e.id;

-- Check hierarchy
SELECT e.full_name, e.position_title, m.full_name as manager
FROM employees e
LEFT JOIN employees m ON e.direct_manager_id = m.id;

-- Check sample data counts
SELECT 
  (SELECT COUNT(*) FROM companies) as companies,
  (SELECT COUNT(*) FROM products WHERE status = 'approved') as products,
  (SELECT COUNT(*) FROM accounts WHERE status = 'approved') as accounts,
  (SELECT COUNT(*) FROM doctors WHERE status = 'approved') as doctors,
  (SELECT COUNT(*) FROM cases) as cases;
```

## Next Steps After Testing

1. **Create Logo Assets**: Follow the logo requirements in the main README
2. **Deploy to Production**: Set up proper hosting and domain
3. **Configure Email**: Set up SMTP for notifications
4. **Add SSL Certificate**: Ensure secure HTTPS connection
5. **Performance Optimization**: Add caching and CDN if needed
6. **User Training**: Provide training materials for end users

## Support

If you encounter any issues during testing:
1. Check browser console for JavaScript errors
2. Verify Supabase connection and credentials
3. Ensure all test data was inserted correctly
4. Check network connectivity and API responses

The system is designed to be robust and user-friendly. Most issues are related to initial setup or data configuration.
