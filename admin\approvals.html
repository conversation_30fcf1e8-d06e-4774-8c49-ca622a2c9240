<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Approvals - Administrator</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">System Approvals</h1>
                            <p class="text-muted mb-0">Final approval for all system submissions</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
                                <i class="fas fa-sync-alt me-2"></i>Refresh All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Approval Summary Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-warning">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingCases">-</h3>
                                    <p class="text-muted mb-0">Pending Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-info">
                                    <i class="fas fa-user-md"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingDoctors">-</h3>
                                    <p class="text-muted mb-0">Pending Doctors</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-primary">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingAccounts">-</h3>
                                    <p class="text-muted mb-0">Pending Accounts</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-success">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingProducts">-</h3>
                                    <p class="text-muted mb-0">Pending Products</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Approval Tabs -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="approvalTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="cases-tab" data-bs-toggle="tab" 
                                            data-bs-target="#cases-pane" type="button" role="tab">
                                        <i class="fas fa-clipboard-list me-2"></i>Cases
                                        <span class="badge bg-warning ms-1" id="casesBadge">0</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="doctors-tab" data-bs-toggle="tab" 
                                            data-bs-target="#doctors-pane" type="button" role="tab">
                                        <i class="fas fa-user-md me-2"></i>Doctors
                                        <span class="badge bg-warning ms-1" id="doctorsBadge">0</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="accounts-tab" data-bs-toggle="tab" 
                                            data-bs-target="#accounts-pane" type="button" role="tab">
                                        <i class="fas fa-building me-2"></i>Accounts
                                        <span class="badge bg-warning ms-1" id="accountsBadge">0</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="products-tab" data-bs-toggle="tab" 
                                            data-bs-target="#products-pane" type="button" role="tab">
                                        <i class="fas fa-box me-2"></i>Products
                                        <span class="badge bg-warning ms-1" id="productsBadge">0</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="tab-content" id="approvalsTabContent">
                            <!-- Cases Tab -->
                            <div class="tab-pane fade show active" id="cases-pane" role="tabpanel">
                                <div class="card-body p-0">
                                    <div id="casesApprovals">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Doctors Tab -->
                            <div class="tab-pane fade" id="doctors-pane" role="tabpanel">
                                <div class="card-body p-0">
                                    <div id="doctorsApprovals">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Accounts Tab -->
                            <div class="tab-pane fade" id="accounts-pane" role="tabpanel">
                                <div class="card-body p-0">
                                    <div id="accountsApprovals">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Products Tab -->
                            <div class="tab-pane fade" id="products-pane" role="tabpanel">
                                <div class="card-body p-0">
                                    <div id="productsApprovals">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Approval Modal -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalModalTitle">Review Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="approvalModalContent">
                        <!-- Content will be loaded here -->
                    </div>
                    
                    <!-- Approval History -->
                    <div id="approvalHistory" class="mt-4">
                        <!-- Approval history will be shown here -->
                    </div>
                    
                    <!-- Comments Section -->
                    <div class="mt-4">
                        <label for="approvalComments" class="form-label">Admin Comments</label>
                        <textarea class="form-control" id="approvalComments" rows="3" 
                                  placeholder="Add your final comments (optional for approval, required for rejection)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="rejectBtn">
                        <i class="fas fa-times me-2"></i>Final Reject
                    </button>
                    <button type="button" class="btn btn-success" id="approveBtn">
                        <i class="fas fa-check me-2"></i>Final Approve
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, getSession, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDateTime, showLoading, hideLoading } from '/js/utils.js';
        
        let currentUser;
        let currentApprovalItem = null;
        let currentApprovalType = null;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['admin'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Get current user
            currentUser = getSession();
            
            // Load initial data
            await loadAllApprovals();
            
            // Setup event listeners
            setupEventListeners();
        });
        
        // Setup event listeners
        function setupEventListeners() {
            // Tab change handlers
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', async (e) => {
                    const targetId = e.target.getAttribute('data-bs-target');
                    
                    switch (targetId) {
                        case '#cases-pane':
                            await loadCasesApprovals();
                            break;
                        case '#doctors-pane':
                            await loadDoctorsApprovals();
                            break;
                        case '#accounts-pane':
                            await loadAccountsApprovals();
                            break;
                        case '#products-pane':
                            await loadProductsApprovals();
                            break;
                    }
                });
            });
            
            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', async () => {
                await loadAllApprovals();
                showToast('All data refreshed', 'success');
            });
            
            // Approval modal buttons
            document.getElementById('approveBtn').addEventListener('click', () => handleApproval('approved'));
            document.getElementById('rejectBtn').addEventListener('click', () => handleApproval('rejected'));
        }
        
        // Load all approvals and update summary
        async function loadAllApprovals() {
            await Promise.all([
                loadCasesApprovals(),
                loadDoctorsApprovals(),
                loadAccountsApprovals(),
                loadProductsApprovals()
            ]);
            
            // Update summary cards
            document.getElementById('pendingCases').textContent = document.getElementById('casesBadge').textContent;
            document.getElementById('pendingDoctors').textContent = document.getElementById('doctorsBadge').textContent;
            document.getElementById('pendingAccounts').textContent = document.getElementById('accountsBadge').textContent;
            document.getElementById('pendingProducts').textContent = document.getElementById('productsBadge').textContent;
        }
        
        // Load cases pending approval
        async function loadCasesApprovals() {
            try {
                const { data: cases, error } = await supabase
                    .from('cases')
                    .select(`
                        *,
                        users!created_by (
                            username,
                            employees (full_name, position_title)
                        ),
                        doctors (name, specialty),
                        accounts (name, type, governorate),
                        case_products (
                            units,
                            companies (name),
                            products (name)
                        )
                    `)
                    .eq('status', 'pending')
                    .order('created_at', { ascending: false });
                
                if (error) throw error;
                
                document.getElementById('casesBadge').textContent = cases?.length || 0;
                displayCasesApprovals(cases || []);
            } catch (error) {
                console.error('Error loading cases approvals:', error);
                displayCasesApprovals([]);
            }
        }
        
        // Load doctors pending approval
        async function loadDoctorsApprovals() {
            try {
                const { data: doctors, error } = await supabase
                    .from('doctors')
                    .select(`
                        *,
                        users!created_by (
                            username,
                            employees (full_name, position_title)
                        )
                    `)
                    .eq('status', 'pending')
                    .order('created_at', { ascending: false });
                
                if (error) throw error;
                
                document.getElementById('doctorsBadge').textContent = doctors?.length || 0;
                displayDoctorsApprovals(doctors || []);
            } catch (error) {
                console.error('Error loading doctors approvals:', error);
                displayDoctorsApprovals([]);
            }
        }
        
        // Load accounts pending approval
        async function loadAccountsApprovals() {
            try {
                const { data: accounts, error } = await supabase
                    .from('accounts')
                    .select(`
                        *,
                        users!created_by (
                            username,
                            employees (full_name, position_title)
                        )
                    `)
                    .eq('status', 'pending')
                    .order('created_at', { ascending: false });
                
                if (error) throw error;
                
                document.getElementById('accountsBadge').textContent = accounts?.length || 0;
                displayAccountsApprovals(accounts || []);
            } catch (error) {
                console.error('Error loading accounts approvals:', error);
                displayAccountsApprovals([]);
            }
        }
        
        // Load products pending approval
        async function loadProductsApprovals() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select(`
                        *,
                        users!created_by (
                            username,
                            employees (full_name, position_title)
                        ),
                        companies (name),
                        categories (name)
                    `)
                    .eq('status', 'pending')
                    .order('created_at', { ascending: false });
                
                if (error) throw error;
                
                document.getElementById('productsBadge').textContent = products?.length || 0;
                displayProductsApprovals(products || []);
            } catch (error) {
                console.error('Error loading products approvals:', error);
                displayProductsApprovals([]);
            }
        }
        
        // Display cases approvals
        function displayCasesApprovals(cases) {
            const container = document.getElementById('casesApprovals');
            
            if (cases.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>No pending case approvals</h5>
                        <p class="mb-0">All cases have been reviewed</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = cases.map(case_ => `
                <div class="approval-item border-bottom p-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-2">Case - ${case_.case_date}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Doctor:</strong> ${case_.doctors?.name || 'N/A'}<br>
                                        <strong>Account:</strong> ${case_.accounts?.name || 'N/A'}<br>
                                        <strong>Submitted by:</strong> ${case_.users?.employees?.full_name || case_.users?.username || 'N/A'}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Products:</strong> ${case_.case_products?.length || 0} items<br>
                                        <strong>Submitted:</strong> ${formatDateTime(case_.created_at)}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="reviewItem('case', ${case_.id})">
                                <i class="fas fa-eye me-1"></i>Review
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Display doctors approvals
        function displayDoctorsApprovals(doctors) {
            const container = document.getElementById('doctorsApprovals');
            
            if (doctors.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>No pending doctor approvals</h5>
                        <p class="mb-0">All doctors have been reviewed</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = doctors.map(doctor => `
                <div class="approval-item border-bottom p-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-2">Dr. ${doctor.name}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Specialty:</strong> ${doctor.specialty || 'N/A'}<br>
                                        <strong>Phone:</strong> ${doctor.phone || 'N/A'}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Submitted by:</strong> ${doctor.users?.employees?.full_name || doctor.users?.username || 'N/A'}<br>
                                        <strong>Submitted:</strong> ${formatDateTime(doctor.created_at)}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="reviewItem('doctor', ${doctor.id})">
                                <i class="fas fa-eye me-1"></i>Review
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Display accounts approvals
        function displayAccountsApprovals(accounts) {
            const container = document.getElementById('accountsApprovals');
            
            if (accounts.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>No pending account approvals</h5>
                        <p class="mb-0">All accounts have been reviewed</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = accounts.map(account => `
                <div class="approval-item border-bottom p-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-2">${account.name}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Type:</strong> ${account.type.toUpperCase()}<br>
                                        <strong>Governorate:</strong> ${account.governorate}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Submitted by:</strong> ${account.users?.employees?.full_name || account.users?.username || 'N/A'}<br>
                                        <strong>Submitted:</strong> ${formatDateTime(account.created_at)}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="reviewItem('account', ${account.id})">
                                <i class="fas fa-eye me-1"></i>Review
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Display products approvals
        function displayProductsApprovals(products) {
            const container = document.getElementById('productsApprovals');
            
            if (products.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>No pending product approvals</h5>
                        <p class="mb-0">All products have been reviewed</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = products.map(product => `
                <div class="approval-item border-bottom p-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-2">${product.name}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Company:</strong> ${product.companies?.name || 'N/A'}<br>
                                        <strong>Category:</strong> ${product.categories?.name || 'N/A'}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="text-muted mb-2">
                                        <strong>Submitted by:</strong> ${product.users?.employees?.full_name || product.users?.username || 'N/A'}<br>
                                        <strong>Submitted:</strong> ${formatDateTime(product.created_at)}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="reviewItem('product', ${product.id})">
                                <i class="fas fa-eye me-1"></i>Review
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Review item function (global)
        window.reviewItem = async function(type, id) {
            currentApprovalType = type;
            currentApprovalItem = { type, id };
            
            // Load item details and show modal
            const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
            document.getElementById('approvalModalTitle').textContent = `Final Review - ${type.charAt(0).toUpperCase() + type.slice(1)}`;
            document.getElementById('approvalModalContent').innerHTML = '<div class="text-center"><div class="spinner-border"></div></div>';
            document.getElementById('approvalComments').value = '';
            
            modal.show();
            
            // Load detailed information
            try {
                let data;
                const tableName = type === 'case' ? 'cases' : 
                                 type === 'doctor' ? 'doctors' :
                                 type === 'account' ? 'accounts' : 'products';
                
                let selectQuery = `
                    *,
                    users!created_by (username, employees (full_name, position_title))
                `;
                
                if (type === 'case') {
                    selectQuery += `,
                        doctors (name, specialty, phone),
                        accounts (name, type, governorate),
                        case_products (units, companies (name), products (name))
                    `;
                } else if (type === 'product') {
                    selectQuery += `,
                        companies (name),
                        categories (name)
                    `;
                }
                
                const { data: itemData } = await supabase
                    .from(tableName)
                    .select(selectQuery)
                    .eq('id', id)
                    .single();
                
                data = itemData;
                
                // Display detailed information
                displayItemDetails(data, type);
                
                // Load approval history
                await loadApprovalHistory(type, id);
                
            } catch (error) {
                console.error('Error loading item details:', error);
                document.getElementById('approvalModalContent').innerHTML = '<div class="alert alert-danger">Error loading details</div>';
            }
        };
        
        // Display item details in modal
        function displayItemDetails(item, type) {
            let content = '';
            
            switch (type) {
                case 'case':
                    content = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Case Information</h6>
                                <p><strong>Date:</strong> ${item.case_date}</p>
                                <p><strong>Doctor:</strong> ${item.doctors?.name || 'N/A'}</p>
                                <p><strong>Account:</strong> ${item.accounts?.name || 'N/A'}</p>
                                <p><strong>Comments:</strong> ${item.comments || 'None'}</p>
                                <p><strong>Submitted by:</strong> ${item.users?.employees?.full_name || item.users?.username || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Products Used</h6>
                                ${item.case_products?.map(cp => `
                                    <div class="mb-2 p-2 border rounded">
                                        <strong>${cp.companies?.name || 'N/A'}</strong><br>
                                        ${cp.products?.name || 'N/A'}<br>
                                        <span class="badge bg-primary">${cp.units} units</span>
                                    </div>
                                `).join('') || 'No products'}
                            </div>
                        </div>
                    `;
                    break;
                case 'doctor':
                    content = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Doctor Information</h6>
                                <p><strong>Name:</strong> ${item.name}</p>
                                <p><strong>Specialty:</strong> ${item.specialty || 'N/A'}</p>
                                <p><strong>Phone:</strong> ${item.phone || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Submission Details</h6>
                                <p><strong>Submitted by:</strong> ${item.users?.employees?.full_name || item.users?.username || 'N/A'}</p>
                                <p><strong>Submitted on:</strong> ${formatDateTime(item.created_at)}</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'account':
                    content = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Account Information</h6>
                                <p><strong>Name:</strong> ${item.name}</p>
                                <p><strong>Type:</strong> ${item.type.toUpperCase()}</p>
                                <p><strong>Governorate:</strong> ${item.governorate}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Submission Details</h6>
                                <p><strong>Submitted by:</strong> ${item.users?.employees?.full_name || item.users?.username || 'N/A'}</p>
                                <p><strong>Submitted on:</strong> ${formatDateTime(item.created_at)}</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'product':
                    content = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Product Information</h6>
                                <p><strong>Name:</strong> ${item.name}</p>
                                <p><strong>Company:</strong> ${item.companies?.name || 'N/A'}</p>
                                <p><strong>Category:</strong> ${item.categories?.name || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Submission Details</h6>
                                <p><strong>Submitted by:</strong> ${item.users?.employees?.full_name || item.users?.username || 'N/A'}</p>
                                <p><strong>Submitted on:</strong> ${formatDateTime(item.created_at)}</p>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            document.getElementById('approvalModalContent').innerHTML = content;
        }
        
        // Load approval history
        async function loadApprovalHistory(type, id) {
            // For now, show placeholder - in a real system you'd have an approval_history table
            document.getElementById('approvalHistory').innerHTML = `
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Approval History</h6>
                        <p class="text-muted mb-0">This item is pending final administrative approval.</p>
                    </div>
                </div>
            `;
        }
        
        // Handle approval/rejection
        async function handleApproval(status) {
            if (!currentApprovalItem) return;
            
            const comments = document.getElementById('approvalComments').value.trim();
            
            if (status === 'rejected' && !comments) {
                showToast('Comments are required for rejection', 'error');
                return;
            }
            
            const approveBtn = document.getElementById('approveBtn');
            const rejectBtn = document.getElementById('rejectBtn');
            
            try {
                showLoading(status === 'approved' ? approveBtn : rejectBtn);
                
                const tableName = currentApprovalItem.type === 'case' ? 'cases' : 
                                 currentApprovalItem.type === 'doctor' ? 'doctors' :
                                 currentApprovalItem.type === 'account' ? 'accounts' : 'products';
                
                const { error } = await supabase
                    .from(tableName)
                    .update({
                        status: status,
                        approved_by: currentUser.id,
                        approved_at: new Date().toISOString(),
                        approval_comments: comments || null
                    })
                    .eq('id', currentApprovalItem.id);
                
                if (error) throw error;
                
                showToast(`${currentApprovalItem.type.charAt(0).toUpperCase() + currentApprovalItem.type.slice(1)} ${status} successfully`, 'success');
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('approvalModal'));
                modal.hide();
                
                // Refresh all data
                await loadAllApprovals();
                
            } catch (error) {
                console.error('Error processing approval:', error);
                showToast('Error processing approval', 'error');
            } finally {
                hideLoading(approveBtn);
                hideLoading(rejectBtn);
            }
        }
    </script>
    
    <style>
        .approval-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .approval-item:last-child {
            border-bottom: none !important;
        }
    </style>
</body>
</html>
