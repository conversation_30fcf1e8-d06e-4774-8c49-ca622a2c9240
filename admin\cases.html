<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Cases - Administrator</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">All System Cases</h1>
                            <p class="text-muted mb-0">View and manage all cases across the system</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
                                <i class="fas fa-sync-alt me-2"></i>Refresh
                            </button>
                            <button class="btn btn-success btn-sm" id="exportBtn">
                                <i class="fas fa-download me-2"></i>Export All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-primary">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="totalCases">-</h3>
                                    <p class="text-muted mb-0">Total Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingCases">-</h3>
                                    <p class="text-muted mb-0">Pending</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="approvedCases">-</h3>
                                    <p class="text-muted mb-0">Approved</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-danger">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="rejectedCases">-</h3>
                                    <p class="text-muted mb-0">Rejected</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div id="filtersContainer" class="mb-4">
                <!-- Filters will be rendered here -->
            </div>
            
            <!-- Cases Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>System Cases
                            </h5>
                            <div class="d-flex gap-2">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" id="refreshTableBtn">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" id="exportTableBtn">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="casesTable">
                                <!-- Smart table will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDate } from '/js/utils.js';
        import { SmartTable } from '/js/tables.js';
        import { initializeFilters } from '/js/filters.js';
        
        let casesTable;
        let filterManager;
        let allCases = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['admin'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize components
            await initializeComponents();
            
            // Load initial data
            await loadCases();
        });
        
        // Initialize page components
        async function initializeComponents() {
            // Initialize filters
            filterManager = initializeFilters('filtersContainer', {
                showDateRange: true,
                showCompany: true,
                showCategory: true,
                showProduct: true,
                showAccount: true,
                showDoctor: true,
                showGovernorate: true,
                showAccountType: true,
                showTextSearch: true,
                showStatus: true,
                showSubmittedBy: true // Admin-specific filter
            });
            
            // Initialize smart table
            casesTable = new SmartTable('casesTable', {
                pageSize: 100,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: false, // Using separate filter component
                columnToggle: true
            });
            
            // Define table columns
            const columns = [
                {
                    key: 'case_date',
                    title: 'Date',
                    type: 'date',
                    width: '100px',
                    sortable: true
                },
                {
                    key: 'submitted_by',
                    title: 'Submitted By',
                    width: '150px',
                    sortable: true,
                    render: (value, row) => {
                        const user = row.users;
                        const employee = user?.employees;
                        return `
                            <div>
                                <strong>${employee?.full_name || user?.username || 'N/A'}</strong>
                                ${employee?.position_title ? `<br><small class="text-muted">${employee.position_title}</small>` : ''}
                            </div>
                        `;
                    }
                },
                {
                    key: 'doctor_name',
                    title: 'Doctor',
                    width: '200px',
                    sortable: true,
                    render: (value, row) => {
                        const specialty = row.doctors?.specialty;
                        return `
                            <div>
                                <strong>${row.doctors?.name || 'N/A'}</strong>
                                ${specialty ? `<br><small class="text-muted">${specialty}</small>` : ''}
                            </div>
                        `;
                    }
                },
                {
                    key: 'account_name',
                    title: 'Account',
                    width: '200px',
                    sortable: true,
                    render: (value, row) => {
                        const account = row.accounts;
                        if (!account) return 'N/A';
                        return `
                            <div>
                                <strong>${account.name}</strong>
                                <br><small class="text-muted">${account.type.toUpperCase()} - ${account.governorate}</small>
                            </div>
                        `;
                    }
                },
                {
                    key: 'products',
                    title: 'Products',
                    width: '300px',
                    sortable: false,
                    render: (value, row) => {
                        const products = row.case_products || [];
                        if (products.length === 0) return 'No products';
                        
                        return products.map(cp => `
                            <div class="mb-1">
                                <strong>${cp.companies?.name || 'N/A'}</strong> - 
                                ${cp.products?.name || 'N/A'}
                                <span class="badge bg-primary ms-1">${cp.units} units</span>
                            </div>
                        `).join('');
                    }
                },
                {
                    key: 'status',
                    title: 'Status',
                    width: '100px',
                    sortable: true,
                    render: (value, row) => {
                        const statusMap = {
                            'pending': 'warning',
                            'approved': 'success',
                            'rejected': 'danger'
                        };
                        const badgeClass = statusMap[value] || 'secondary';
                        return `<span class="badge bg-${badgeClass}">${value.charAt(0).toUpperCase() + value.slice(1)}</span>`;
                    }
                },
                {
                    key: 'comments',
                    title: 'Comments',
                    width: '200px',
                    sortable: false,
                    render: (value, row) => {
                        if (!value) return '<span class="text-muted">No comments</span>';
                        return value.length > 50 ? 
                            `<span title="${value}">${value.substring(0, 50)}...</span>` : 
                            value;
                    }
                }
            ];
            
            casesTable.setColumns(columns);
            
            // Set up filter callback
            filterManager.onFiltersChange(async (filters) => {
                await loadCases(filters);
            });
            
            // Set up refresh buttons
            document.getElementById('refreshBtn').addEventListener('click', async () => {
                await loadCases(filterManager.getFilters());
                showToast('Cases refreshed', 'success');
            });
            
            document.getElementById('refreshTableBtn').addEventListener('click', async () => {
                await loadCases(filterManager.getFilters());
                showToast('Table refreshed', 'success');
            });
            
            // Set up export buttons
            document.getElementById('exportBtn').addEventListener('click', () => {
                casesTable.exportToExcel(allCases, 'all_system_cases_export');
            });
            
            document.getElementById('exportTableBtn').addEventListener('click', () => {
                casesTable.exportToExcel(casesTable.filteredData, 'filtered_cases_export');
            });
        }
        
        // Load cases data
        async function loadCases(filters = {}) {
            try {
                // Build query
                let query = supabase
                    .from('cases')
                    .select(`
                        *,
                        users!created_by (
                            username,
                            employees (full_name, position_title)
                        ),
                        doctors (name, specialty),
                        accounts (name, type, governorate),
                        case_products (
                            units,
                            companies (name),
                            products (name)
                        )
                    `)
                    .order('created_at', { ascending: false });
                
                // Apply filters
                if (filters.status && filters.status !== 'all') {
                    query = query.eq('status', filters.status);
                }
                
                if (filters.dateFrom) {
                    query = query.gte('case_date', filters.dateFrom);
                }
                
                if (filters.dateTo) {
                    query = query.lte('case_date', filters.dateTo);
                }
                
                const { data: cases, error } = await query;
                
                if (error) throw error;
                
                allCases = cases || [];
                
                // Update summary cards
                updateSummaryCards(allCases);
                
                // Apply additional client-side filters
                let filteredCases = allCases;
                
                if (filters.textSearch) {
                    const searchTerm = filters.textSearch.toLowerCase();
                    filteredCases = filteredCases.filter(case_ => 
                        case_.doctors?.name?.toLowerCase().includes(searchTerm) ||
                        case_.accounts?.name?.toLowerCase().includes(searchTerm) ||
                        case_.comments?.toLowerCase().includes(searchTerm) ||
                        case_.users?.employees?.full_name?.toLowerCase().includes(searchTerm)
                    );
                }
                
                if (filters.company && filters.company !== 'all') {
                    filteredCases = filteredCases.filter(case_ => 
                        case_.case_products?.some(cp => cp.companies?.id == filters.company)
                    );
                }
                
                if (filters.governorate && filters.governorate !== 'all') {
                    filteredCases = filteredCases.filter(case_ => 
                        case_.accounts?.governorate === filters.governorate
                    );
                }
                
                if (filters.accountType && filters.accountType !== 'all') {
                    filteredCases = filteredCases.filter(case_ => 
                        case_.accounts?.type === filters.accountType
                    );
                }
                
                casesTable.setData(filteredCases);
                
                if (filteredCases.length === 0 && Object.keys(filters).length > 0) {
                    showToast('No cases found matching the current filters', 'info');
                }
            } catch (error) {
                console.error('Error loading cases:', error);
                showToast('Error loading cases', 'error');
                casesTable.setData([]);
                updateSummaryCards([]);
            }
        }
        
        // Update summary cards
        function updateSummaryCards(cases) {
            const total = cases.length;
            const pending = cases.filter(c => c.status === 'pending').length;
            const approved = cases.filter(c => c.status === 'approved').length;
            const rejected = cases.filter(c => c.status === 'rejected').length;
            
            document.getElementById('totalCases').textContent = total;
            document.getElementById('pendingCases').textContent = pending;
            document.getElementById('approvedCases').textContent = approved;
            document.getElementById('rejectedCases').textContent = rejected;
        }
    </script>
    
    <style>
        /* Custom styles for cases table */
        .table th {
            white-space: nowrap;
        }
        
        .table td {
            vertical-align: top;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        /* Responsive table adjustments */
        @media (max-width: 768px) {
            .table-responsive {
                font-size: 0.875rem;
            }
            
            .table td {
                padding: 0.5rem 0.25rem;
            }
        }
    </style>
</body>
</html>
