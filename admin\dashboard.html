<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Market Tracking System</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="welcome-header">
                        <h1 class="h3 mb-1 text-gradient">Administrator Dashboard</h1>
                        <p class="text-muted mb-0">System overview and management</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-primary">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="totalUsers">-</h3>
                                    <p class="text-muted mb-0">Total Users</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-success">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="totalCases">-</h3>
                                    <p class="text-muted mb-0">Total Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingApprovals">-</h3>
                                    <p class="text-muted mb-0">Pending Approvals</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-info">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="totalAccounts">-</h3>
                                    <p class="text-muted mb-0">Total Accounts</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-xl-8 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Cases Trend (Last 30 Days)</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" id="exportCasesChart">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="casesTrendChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-4 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">User Roles</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" id="exportRolesChart">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="userRolesChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions and Recent Activity -->
            <div class="row">
                <div class="col-xl-4 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/admin/users.html" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>Add New User
                                </a>
                                <a href="/admin/approvals.html" class="btn btn-outline-warning">
                                    <i class="fas fa-tasks me-2"></i>Review Approvals
                                </a>
                                <a href="/admin/master-data.html" class="btn btn-outline-info">
                                    <i class="fas fa-database me-2"></i>Manage Master Data
                                </a>
                                <a href="/admin/bulk-upload.html" class="btn btn-outline-success">
                                    <i class="fas fa-upload me-2"></i>Bulk Upload
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-8 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Recent Activity
                            </h5>
                            <a href="/admin/cases.html" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <div id="recentActivity">
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDate, formatDateTime } from '/js/utils.js';
        import { createChart, exportChart } from '/js/charts.js';
        
        let casesTrendChart;
        let userRolesChart;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['admin'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load dashboard data
            await loadDashboardData();
            
            // Setup export buttons
            setupExportButtons();
        });
        
        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadMetrics(),
                    loadCasesTrend(),
                    loadUserRoles(),
                    loadRecentActivity()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showToast('Error loading dashboard data', 'error');
            }
        }
        
        // Load key metrics
        async function loadMetrics() {
            try {
                const [usersResult, casesResult, pendingResult, accountsResult] = await Promise.all([
                    supabase.from('users').select('id', { count: 'exact', head: true }),
                    supabase.from('cases').select('id', { count: 'exact', head: true }),
                    supabase.from('cases').select('id', { count: 'exact', head: true }).eq('status', 'pending'),
                    supabase.from('accounts').select('id', { count: 'exact', head: true })
                ]);
                
                document.getElementById('totalUsers').textContent = usersResult.count || 0;
                document.getElementById('totalCases').textContent = casesResult.count || 0;
                document.getElementById('pendingApprovals').textContent = pendingResult.count || 0;
                document.getElementById('totalAccounts').textContent = accountsResult.count || 0;
            } catch (error) {
                console.error('Error loading metrics:', error);
            }
        }
        
        // Load cases trend chart
        async function loadCasesTrend() {
            try {
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                
                const { data: cases, error } = await supabase
                    .from('cases')
                    .select('case_date, status')
                    .gte('case_date', thirtyDaysAgo.toISOString().split('T')[0])
                    .order('case_date');
                
                if (error) throw error;
                
                // Group cases by date
                const casesData = {};
                const last30Days = [];
                
                for (let i = 29; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    const dateStr = date.toISOString().split('T')[0];
                    last30Days.push(dateStr);
                    casesData[dateStr] = { approved: 0, pending: 0, rejected: 0 };
                }
                
                cases.forEach(case_ => {
                    const dateStr = case_.case_date;
                    if (casesData[dateStr]) {
                        casesData[dateStr][case_.status]++;
                    }
                });
                
                const chartData = {
                    labels: last30Days.map(date => new Date(date).toLocaleDateString()),
                    datasets: [
                        {
                            label: 'Approved',
                            data: last30Days.map(date => casesData[date].approved),
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Pending',
                            data: last30Days.map(date => casesData[date].pending),
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Rejected',
                            data: last30Days.map(date => casesData[date].rejected),
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            tension: 0.4
                        }
                    ]
                };
                
                casesTrendChart = createChart('casesTrendChart', 'line', chartData, {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading cases trend:', error);
            }
        }
        
        // Load user roles chart
        async function loadUserRoles() {
            try {
                const { data: users, error } = await supabase
                    .from('users')
                    .select('role')
                    .eq('is_active', true);
                
                if (error) throw error;
                
                const roleCounts = {};
                users.forEach(user => {
                    roleCounts[user.role] = (roleCounts[user.role] || 0) + 1;
                });
                
                const roleLabels = {
                    'product_specialist': 'Product Specialists',
                    'district_manager': 'District Managers',
                    'senior_manager': 'Senior Managers',
                    'admin': 'Administrators'
                };
                
                const chartData = {
                    labels: Object.keys(roleCounts).map(role => roleLabels[role] || role),
                    datasets: [{
                        data: Object.values(roleCounts),
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c'
                        ]
                    }]
                };
                
                userRolesChart = createChart('userRolesChart', 'doughnut', chartData, {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading user roles:', error);
            }
        }
        
        // Load recent activity
        async function loadRecentActivity() {
            try {
                const { data: recentCases, error } = await supabase
                    .from('cases')
                    .select(`
                        *,
                        users!created_by (
                            username,
                            employees (full_name)
                        ),
                        doctors (name),
                        accounts (name, type, governorate)
                    `)
                    .order('created_at', { ascending: false })
                    .limit(10);
                
                if (error) throw error;
                
                const activityContainer = document.getElementById('recentActivity');
                
                if (!recentCases || recentCases.length === 0) {
                    activityContainer.innerHTML = `
                        <div class="text-center py-3 text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p class="mb-0">No recent activity</p>
                        </div>
                    `;
                    return;
                }
                
                activityContainer.innerHTML = recentCases.map(case_ => {
                    const statusBadge = getStatusBadge(case_.status);
                    const userName = case_.users?.employees?.full_name || case_.users?.username || 'Unknown';
                    
                    return `
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="activity-icon me-3">
                                <i class="fas fa-clipboard-list text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">Case submitted by ${userName}</h6>
                                        <p class="text-muted mb-1 small">
                                            Dr. ${case_.doctors?.name || 'N/A'} at ${case_.accounts?.name || 'N/A'}
                                        </p>
                                        <small class="text-muted">${formatDateTime(case_.created_at)}</small>
                                    </div>
                                    <div>
                                        ${statusBadge}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            } catch (error) {
                console.error('Error loading recent activity:', error);
                document.getElementById('recentActivity').innerHTML = `
                    <div class="text-center py-3 text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p class="mb-0">Error loading recent activity</p>
                    </div>
                `;
            }
        }
        
        // Get status badge HTML
        function getStatusBadge(status) {
            const statusMap = {
                'pending': 'warning',
                'approved': 'success',
                'rejected': 'danger'
            };
            const badgeClass = statusMap[status] || 'secondary';
            return `<span class="badge bg-${badgeClass}">${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;
        }
        
        // Setup export buttons
        function setupExportButtons() {
            document.getElementById('exportCasesChart').addEventListener('click', () => {
                if (casesTrendChart) {
                    exportChart(casesTrendChart, 'cases_trend_chart');
                }
            });
            
            document.getElementById('exportRolesChart').addEventListener('click', () => {
                if (userRolesChart) {
                    exportChart(userRolesChart, 'user_roles_chart');
                }
            });
        }
    </script>
    
    <style>
        .activity-item {
            border-bottom: 1px solid #eee;
            padding-bottom: 1rem;
        }
        
        .activity-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</body>
</html>
