<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Master Data Management - Administrator</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Master Data Management</h1>
                            <p class="text-muted mb-0">Manage companies, categories, and other master data</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
                                <i class="fas fa-sync-alt me-2"></i>Refresh All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Master Data Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="masterDataTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="companies-tab" data-bs-toggle="tab" 
                                            data-bs-target="#companies-pane" type="button" role="tab">
                                        <i class="fas fa-industry me-2"></i>Companies
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="categories-tab" data-bs-toggle="tab" 
                                            data-bs-target="#categories-pane" type="button" role="tab">
                                        <i class="fas fa-tags me-2"></i>Categories
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="tab-content" id="masterDataTabContent">
                            <!-- Companies Tab -->
                            <div class="tab-pane fade show active" id="companies-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Companies</h6>
                                        <button class="btn btn-primary btn-sm" id="addCompanyBtn">
                                            <i class="fas fa-plus me-1"></i>Add Company
                                        </button>
                                    </div>
                                    
                                    <div id="companiesTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categories Tab -->
                            <div class="tab-pane fade" id="categories-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Categories</h6>
                                        <button class="btn btn-primary btn-sm" id="addCategoryBtn">
                                            <i class="fas fa-plus me-1"></i>Add Category
                                        </button>
                                    </div>
                                    
                                    <div id="categoriesTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add/Edit Company Modal -->
    <div class="modal fade" id="companyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="companyModalTitle">Add Company</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="companyForm">
                    <div class="modal-body">
                        <input type="hidden" id="companyId" name="id">
                        
                        <div class="mb-3">
                            <label for="companyName" class="form-label">Company Name *</label>
                            <input type="text" class="form-control" id="companyName" name="name" required>
                            <div class="invalid-feedback">
                                Please provide a company name.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="companyDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="companyDescription" name="description" rows="3" 
                                      placeholder="Optional description of the company"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="saveCompanyBtn">
                            <i class="fas fa-save me-2"></i>Save Company
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Category Modal -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalTitle">Add Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="categoryForm">
                    <div class="modal-body">
                        <input type="hidden" id="categoryId" name="id">
                        
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">Category Name *</label>
                            <input type="text" class="form-control" id="categoryName" name="name" required>
                            <div class="invalid-feedback">
                                Please provide a category name.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="categoryDescription" name="description" rows="3" 
                                      placeholder="Optional description of the category"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="saveCategoryBtn">
                            <i class="fas fa-save me-2"></i>Save Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this item?</p>
                    <p class="text-danger"><strong>Warning:</strong> This action cannot be undone and may affect related data.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, formatDateTime } from '/js/utils.js';
        
        let currentDeleteItem = null;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['admin'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load initial data
            await loadCompanies();
            
            // Setup event listeners
            setupEventListeners();
        });
        
        // Setup event listeners
        function setupEventListeners() {
            // Tab change handlers
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', async (e) => {
                    const targetId = e.target.getAttribute('data-bs-target');
                    
                    switch (targetId) {
                        case '#companies-pane':
                            await loadCompanies();
                            break;
                        case '#categories-pane':
                            await loadCategories();
                            break;
                    }
                });
            });
            
            // Add buttons
            document.getElementById('addCompanyBtn').addEventListener('click', () => showCompanyModal());
            document.getElementById('addCategoryBtn').addEventListener('click', () => showCategoryModal());
            
            // Form submissions
            document.getElementById('companyForm').addEventListener('submit', handleCompanySubmit);
            document.getElementById('categoryForm').addEventListener('submit', handleCategorySubmit);
            
            // Delete confirmation
            document.getElementById('confirmDeleteBtn').addEventListener('click', handleDelete);
            
            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', async () => {
                const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
                switch (activeTab) {
                    case '#companies-pane':
                        await loadCompanies();
                        break;
                    case '#categories-pane':
                        await loadCategories();
                        break;
                }
                showToast('Data refreshed', 'success');
            });
        }
        
        // Load companies
        async function loadCompanies() {
            try {
                const { data: companies, error } = await supabase
                    .from('companies')
                    .select('*')
                    .order('name');
                
                if (error) throw error;
                
                displayCompanies(companies || []);
            } catch (error) {
                console.error('Error loading companies:', error);
                showToast('Error loading companies', 'error');
                displayCompanies([]);
            }
        }
        
        // Load categories
        async function loadCategories() {
            try {
                const { data: categories, error } = await supabase
                    .from('categories')
                    .select('*')
                    .order('name');
                
                if (error) throw error;
                
                displayCategories(categories || []);
            } catch (error) {
                console.error('Error loading categories:', error);
                showToast('Error loading categories', 'error');
                displayCategories([]);
            }
        }
        
        // Display companies
        function displayCompanies(companies) {
            const container = document.getElementById('companiesTable');
            
            if (companies.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-industry fa-3x mb-3"></i>
                        <h5>No companies found</h5>
                        <p class="mb-0">Add your first company to get started</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Created</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${companies.map(company => `
                                <tr>
                                    <td><strong>${company.name}</strong></td>
                                    <td>${company.description || '<span class="text-muted">No description</span>'}</td>
                                    <td>${formatDateTime(company.created_at)}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editCompany(${company.id})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteItem('company', ${company.id}, '${company.name}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
        
        // Display categories
        function displayCategories(categories) {
            const container = document.getElementById('categoriesTable');
            
            if (categories.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-tags fa-3x mb-3"></i>
                        <h5>No categories found</h5>
                        <p class="mb-0">Add your first category to get started</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Created</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${categories.map(category => `
                                <tr>
                                    <td><strong>${category.name}</strong></td>
                                    <td>${category.description || '<span class="text-muted">No description</span>'}</td>
                                    <td>${formatDateTime(category.created_at)}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editCategory(${category.id})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteItem('category', ${category.id}, '${category.name}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
        
        // Show company modal
        function showCompanyModal(company = null) {
            const modal = new bootstrap.Modal(document.getElementById('companyModal'));
            const form = document.getElementById('companyForm');
            
            if (company) {
                document.getElementById('companyModalTitle').textContent = 'Edit Company';
                document.getElementById('companyId').value = company.id;
                document.getElementById('companyName').value = company.name;
                document.getElementById('companyDescription').value = company.description || '';
            } else {
                document.getElementById('companyModalTitle').textContent = 'Add Company';
                form.reset();
                document.getElementById('companyId').value = '';
            }
            
            modal.show();
        }
        
        // Show category modal
        function showCategoryModal(category = null) {
            const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
            const form = document.getElementById('categoryForm');
            
            if (category) {
                document.getElementById('categoryModalTitle').textContent = 'Edit Category';
                document.getElementById('categoryId').value = category.id;
                document.getElementById('categoryName').value = category.name;
                document.getElementById('categoryDescription').value = category.description || '';
            } else {
                document.getElementById('categoryModalTitle').textContent = 'Add Category';
                form.reset();
                document.getElementById('categoryId').value = '';
            }
            
            modal.show();
        }
        
        // Handle company form submission
        async function handleCompanySubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('saveCompanyBtn');
            
            if (!validateForm(form)) return;
            
            try {
                showLoading(submitBtn);
                
                const formData = new FormData(form);
                const companyData = {
                    name: formData.get('name').trim(),
                    description: formData.get('description').trim() || null
                };
                
                const companyId = formData.get('id');
                
                let result;
                if (companyId) {
                    // Update existing company
                    result = await supabase
                        .from('companies')
                        .update(companyData)
                        .eq('id', companyId);
                } else {
                    // Create new company
                    result = await supabase
                        .from('companies')
                        .insert([companyData]);
                }
                
                if (result.error) throw result.error;
                
                showToast(`Company ${companyId ? 'updated' : 'created'} successfully`, 'success');
                
                // Close modal and refresh data
                const modal = bootstrap.Modal.getInstance(document.getElementById('companyModal'));
                modal.hide();
                await loadCompanies();
                
            } catch (error) {
                console.error('Error saving company:', error);
                showToast('Error saving company', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
        
        // Handle category form submission
        async function handleCategorySubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('saveCategoryBtn');
            
            if (!validateForm(form)) return;
            
            try {
                showLoading(submitBtn);
                
                const formData = new FormData(form);
                const categoryData = {
                    name: formData.get('name').trim(),
                    description: formData.get('description').trim() || null
                };
                
                const categoryId = formData.get('id');
                
                let result;
                if (categoryId) {
                    // Update existing category
                    result = await supabase
                        .from('categories')
                        .update(categoryData)
                        .eq('id', categoryId);
                } else {
                    // Create new category
                    result = await supabase
                        .from('categories')
                        .insert([categoryData]);
                }
                
                if (result.error) throw result.error;
                
                showToast(`Category ${categoryId ? 'updated' : 'created'} successfully`, 'success');
                
                // Close modal and refresh data
                const modal = bootstrap.Modal.getInstance(document.getElementById('categoryModal'));
                modal.hide();
                await loadCategories();
                
            } catch (error) {
                console.error('Error saving category:', error);
                showToast('Error saving category', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
        
        // Handle delete
        async function handleDelete() {
            if (!currentDeleteItem) return;
            
            const deleteBtn = document.getElementById('confirmDeleteBtn');
            
            try {
                showLoading(deleteBtn);
                
                const tableName = currentDeleteItem.type === 'company' ? 'companies' : 'categories';
                
                const { error } = await supabase
                    .from(tableName)
                    .delete()
                    .eq('id', currentDeleteItem.id);
                
                if (error) throw error;
                
                showToast(`${currentDeleteItem.type.charAt(0).toUpperCase() + currentDeleteItem.type.slice(1)} deleted successfully`, 'success');
                
                // Close modal and refresh data
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                modal.hide();
                
                if (currentDeleteItem.type === 'company') {
                    await loadCompanies();
                } else {
                    await loadCategories();
                }
                
            } catch (error) {
                console.error('Error deleting item:', error);
                showToast('Error deleting item', 'error');
            } finally {
                hideLoading(deleteBtn);
                currentDeleteItem = null;
            }
        }
        
        // Global functions for buttons
        window.editCompany = async function(id) {
            try {
                const { data: company, error } = await supabase
                    .from('companies')
                    .select('*')
                    .eq('id', id)
                    .single();
                
                if (error) throw error;
                
                showCompanyModal(company);
            } catch (error) {
                console.error('Error loading company:', error);
                showToast('Error loading company', 'error');
            }
        };
        
        window.editCategory = async function(id) {
            try {
                const { data: category, error } = await supabase
                    .from('categories')
                    .select('*')
                    .eq('id', id)
                    .single();
                
                if (error) throw error;
                
                showCategoryModal(category);
            } catch (error) {
                console.error('Error loading category:', error);
                showToast('Error loading category', 'error');
            }
        };
        
        window.deleteItem = function(type, id, name) {
            currentDeleteItem = { type, id, name };
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        };
    </script>
</body>
</html>
