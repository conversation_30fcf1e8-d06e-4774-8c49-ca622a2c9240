<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Administrator</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">User Management</h1>
                            <p class="text-muted mb-0">Manage employees and user accounts</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus me-2"></i>Add New User
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>System Users
                            </h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Employee Code</th>
                                            <th>Full Name</th>
                                            <th>Position</th>
                                            <th>Area/Line</th>
                                            <th>Username</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addUserForm">
                    <div class="modal-body">
                        <div class="row">
                            <!-- Employee Information -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Employee Information</h6>
                                
                                <div class="mb-3">
                                    <label for="employeeCode" class="form-label">Employee Code *</label>
                                    <input type="text" class="form-control" id="employeeCode" name="code_number" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="fullName" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="fullName" name="full_name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="positionTitle" class="form-label">Position Title *</label>
                                    <select class="form-select" id="positionTitle" name="position_title" required>
                                        <option value="">Select Position</option>
                                        <option value="Product Specialist">Product Specialist</option>
                                        <option value="District Manager">District Manager</option>
                                        <option value="Senior Manager">Senior Manager</option>
                                        <option value="Admin">Admin</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="line" class="form-label">Line</label>
                                    <input type="text" class="form-control" id="line" name="line" placeholder="e.g., Orthopedic, Cardiac">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="area" class="form-label">Area</label>
                                    <input type="text" class="form-control" id="area" name="area" placeholder="e.g., North, South, Central">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="governorate" class="form-label">Governorate</label>
                                    <select class="form-select" id="governorate" name="governorate">
                                        <option value="">Select Governorate</option>
                                        <option value="Cairo">Cairo</option>
                                        <option value="Alexandria">Alexandria</option>
                                        <option value="Giza">Giza</option>
                                        <option value="Qalyubia">Qalyubia</option>
                                        <option value="Port Said">Port Said</option>
                                        <option value="Suez">Suez</option>
                                        <option value="Luxor">Luxor</option>
                                        <option value="Aswan">Aswan</option>
                                        <option value="Asyut">Asyut</option>
                                        <option value="Beheira">Beheira</option>
                                        <option value="Beni Suef">Beni Suef</option>
                                        <option value="Dakahlia">Dakahlia</option>
                                        <option value="Damietta">Damietta</option>
                                        <option value="Fayyum">Fayyum</option>
                                        <option value="Gharbia">Gharbia</option>
                                        <option value="Ismailia">Ismailia</option>
                                        <option value="Kafr el-Sheikh">Kafr el-Sheikh</option>
                                        <option value="Matrouh">Matrouh</option>
                                        <option value="Minya">Minya</option>
                                        <option value="Monufia">Monufia</option>
                                        <option value="New Valley">New Valley</option>
                                        <option value="North Sinai">North Sinai</option>
                                        <option value="Qena">Qena</option>
                                        <option value="Red Sea">Red Sea</option>
                                        <option value="Sharqia">Sharqia</option>
                                        <option value="Sohag">Sohag</option>
                                        <option value="South Sinai">South Sinai</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="directManager" class="form-label">Direct Manager</label>
                                    <select class="form-select" id="directManager" name="direct_manager_id">
                                        <option value="">No Manager (Top Level)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- User Account Information -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">User Account</h6>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password_plain" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="role" class="form-label">System Role *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="">Select Role</option>
                                        <option value="product_specialist">Product Specialist</option>
                                        <option value="district_manager">District Manager</option>
                                        <option value="senior_manager">Senior Manager</option>
                                        <option value="admin">Administrator</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                        <label class="form-check-label" for="isActive">
                                            Active User
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Role Information -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">Role Hierarchy</h6>
                                    <ul class="mb-0 small">
                                        <li><strong>Product Specialist:</strong> Can add cases, doctors, accounts, products</li>
                                        <li><strong>District Manager:</strong> Approves PS submissions, manages team</li>
                                        <li><strong>Senior Manager:</strong> Approves DM submissions, manages larger teams</li>
                                        <li><strong>Administrator:</strong> Full system access, final approvals</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="saveUserBtn">
                            <i class="fas fa-save me-2"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, formatDateTime } from '/js/utils.js';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['admin'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load initial data
            await loadUsers();
            await loadManagers();
            
            // Setup event listeners
            setupEventListeners();
        });
        
        // Setup event listeners
        function setupEventListeners() {
            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', loadUsers);
            
            // Add user form
            document.getElementById('addUserForm').addEventListener('submit', handleAddUser);
            
            // Position title change - update role automatically
            document.getElementById('positionTitle').addEventListener('change', (e) => {
                const roleSelect = document.getElementById('role');
                const positionMap = {
                    'Product Specialist': 'product_specialist',
                    'District Manager': 'district_manager',
                    'Senior Manager': 'senior_manager',
                    'Admin': 'admin'
                };
                roleSelect.value = positionMap[e.target.value] || '';
            });
        }
        
        // Load users
        async function loadUsers() {
            try {
                const { data: users, error } = await supabase
                    .from('users')
                    .select(`
                        *,
                        employees (
                            *,
                            manager:employees!direct_manager_id (
                                full_name
                            )
                        )
                    `)
                    .order('created_at', { ascending: false });
                
                if (error) throw error;
                
                displayUsers(users || []);
            } catch (error) {
                console.error('Error loading users:', error);
                showToast('Error loading users', 'error');
            }
        }
        
        // Display users in table
        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            
            if (users.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4 text-muted">
                            No users found
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = users.map(user => {
                const employee = user.employees;
                const statusBadge = user.is_active ? 
                    '<span class="badge bg-success">Active</span>' : 
                    '<span class="badge bg-danger">Inactive</span>';
                
                return `
                    <tr>
                        <td>${employee?.code_number || 'N/A'}</td>
                        <td>${employee?.full_name || 'N/A'}</td>
                        <td>${employee?.position_title || 'N/A'}</td>
                        <td>
                            ${employee?.area || 'N/A'} / ${employee?.line || 'N/A'}
                            <br><small class="text-muted">${employee?.governorate || 'N/A'}</small>
                        </td>
                        <td>${user.username}</td>
                        <td>
                            <span class="badge bg-primary">${getRoleDisplayName(user.role)}</span>
                        </td>
                        <td>${statusBadge}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-${user.is_active ? 'warning' : 'success'}" 
                                        onclick="toggleUserStatus(${user.id}, ${user.is_active})" 
                                        title="${user.is_active ? 'Deactivate' : 'Activate'}">
                                    <i class="fas fa-${user.is_active ? 'pause' : 'play'}"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        // Load managers for dropdown
        async function loadManagers() {
            try {
                const { data: managers, error } = await supabase
                    .from('employees')
                    .select('id, full_name, position_title')
                    .in('position_title', ['District Manager', 'Senior Manager', 'Admin'])
                    .order('full_name');
                
                if (error) throw error;
                
                const managerSelect = document.getElementById('directManager');
                managerSelect.innerHTML = '<option value="">No Manager (Top Level)</option>';
                
                managers.forEach(manager => {
                    const option = document.createElement('option');
                    option.value = manager.id;
                    option.textContent = `${manager.full_name} (${manager.position_title})`;
                    managerSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading managers:', error);
            }
        }
        
        // Handle add user form submission
        async function handleAddUser(e) {
            e.preventDefault();
            
            const form = e.target;
            const saveBtn = document.getElementById('saveUserBtn');
            
            if (!validateForm(form)) return;
            
            try {
                showLoading(saveBtn);
                
                // Create employee first
                const employeeData = {
                    code_number: document.getElementById('employeeCode').value.trim(),
                    full_name: document.getElementById('fullName').value.trim(),
                    position_title: document.getElementById('positionTitle').value,
                    line: document.getElementById('line').value.trim() || null,
                    area: document.getElementById('area').value.trim() || null,
                    governorate: document.getElementById('governorate').value || null,
                    direct_manager_id: document.getElementById('directManager').value || null
                };
                
                const { data: employee, error: employeeError } = await supabase
                    .from('employees')
                    .insert(employeeData)
                    .select()
                    .single();
                
                if (employeeError) throw employeeError;
                
                // Create user account
                const userData = {
                    username: document.getElementById('username').value.trim(),
                    password_plain: document.getElementById('password').value,
                    role: document.getElementById('role').value,
                    employee_id: employee.id,
                    is_active: document.getElementById('isActive').checked
                };
                
                const { error: userError } = await supabase
                    .from('users')
                    .insert(userData);
                
                if (userError) throw userError;
                
                showToast('User created successfully!', 'success');
                
                // Reset form and close modal
                form.reset();
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();
                
                // Reload users
                await loadUsers();
                await loadManagers();
                
            } catch (error) {
                console.error('Error creating user:', error);
                if (error.message.includes('duplicate key')) {
                    if (error.message.includes('username')) {
                        showToast('Username already exists', 'error');
                    } else if (error.message.includes('code_number')) {
                        showToast('Employee code already exists', 'error');
                    } else {
                        showToast('Duplicate entry found', 'error');
                    }
                } else {
                    showToast(error.message || 'Error creating user', 'error');
                }
            } finally {
                hideLoading(saveBtn);
            }
        }
        
        // Get role display name
        function getRoleDisplayName(role) {
            const roleNames = {
                'product_specialist': 'Product Specialist',
                'district_manager': 'District Manager',
                'senior_manager': 'Senior Manager',
                'admin': 'Administrator'
            };
            return roleNames[role] || role;
        }
        
        // Toggle user status
        window.toggleUserStatus = async function(userId, currentStatus) {
            try {
                const { error } = await supabase
                    .from('users')
                    .update({ is_active: !currentStatus })
                    .eq('id', userId);
                
                if (error) throw error;
                
                showToast(`User ${currentStatus ? 'deactivated' : 'activated'} successfully`, 'success');
                await loadUsers();
            } catch (error) {
                console.error('Error updating user status:', error);
                showToast('Error updating user status', 'error');
            }
        };
        
        // Edit user (placeholder)
        window.editUser = function(userId) {
            showToast('Edit functionality coming soon', 'info');
        };
    </script>
</body>
</html>
