<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="120" viewBox="0 0 300 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mainLogoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="60" cy="60" r="50" fill="url(#mainLogoGradient)" />
  
  <!-- Medical cross -->
  <rect x="50" y="30" width="20" height="60" fill="white" rx="3"/>
  <rect x="30" y="50" width="60" height="20" fill="white" rx="3"/>
  
  <!-- Company text -->
  <text x="130" y="45" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#333">
    Market Tracking
  </text>
  <text x="130" y="75" font-family="Inter, Arial, sans-serif" font-size="18" font-weight="400" fill="#666">
    Healthcare Management System
  </text>
</svg>
