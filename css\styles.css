/* Market Tracking System - Custom Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
    
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* Header Styles */
.main-header {
    background: var(--primary-gradient);
    height: var(--header-height);
    box-shadow: var(--box-shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    transition: var(--transition);
}

.main-header.sidebar-open {
    left: var(--sidebar-width);
}

.header-brand {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
}

.header-brand:hover {
    color: white;
    text-decoration: none;
}

.header-logo {
    height: 40px;
    width: auto;
    margin-right: 12px;
}

.user-info {
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: -var(--sidebar-width);
    width: var(--sidebar-width);
    height: 100vh;
    background: white;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    z-index: 1040;
    overflow-y: auto;
}

.sidebar.show {
    left: 0;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: var(--primary-gradient);
    color: white;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.3rem;
}

.sidebar-logo {
    height: 45px;
    width: auto;
    margin-right: 12px;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #6c757d;
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    text-decoration: none;
}

.nav-link.active {
    background: var(--primary-gradient);
    color: white;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: white;
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    opacity: 0.7;
}

.nav-link.active .nav-icon {
    opacity: 1;
}

/* Main Content */
.main-content {
    margin-left: 0;
    margin-top: var(--header-height);
    padding: 30px;
    transition: var(--transition);
    min-height: calc(100vh - var(--header-height));
}

.main-content.sidebar-open {
    margin-left: var(--sidebar-width);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 20px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    background: var(--primary-gradient);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: var(--success-gradient);
    border: none;
}

.btn-success:hover {
    background: var(--success-gradient);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
    color: #333;
}

.btn-warning:hover {
    background: var(--warning-gradient);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(67, 233, 123, 0.4);
    color: #333;
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
}

.btn-danger:hover {
    background: var(--danger-gradient);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(250, 112, 154, 0.4);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody td {
    padding: 15px;
    border-color: #f8f9fa;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
    }
    
    .main-header.sidebar-open {
        left: 0;
    }
    
    .main-content.sidebar-open {
        margin-left: 0;
    }
    
    .sidebar {
        width: 100%;
        left: -100%;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .main-content {
        padding: 20px 15px;
    }
}

/* Utility Classes */
.gradient-primary {
    background: var(--primary-gradient);
}

.gradient-secondary {
    background: var(--secondary-gradient);
}

.gradient-success {
    background: var(--success-gradient);
}

.gradient-warning {
    background: var(--warning-gradient);
}

.gradient-danger {
    background: var(--danger-gradient);
}

.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-gradient);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}
