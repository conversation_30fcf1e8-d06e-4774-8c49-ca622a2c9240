<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Tracking System</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
</head>
<body>
    <script type="module">
        import { isAuthenticated, getSession } from '/js/auth.js';
        
        // Check if user is authenticated
        if (!isAuthenticated()) {
            // Redirect to login page
            window.location.href = '/login.html';
        } else {
            // Redirect to appropriate dashboard based on role
            const session = getSession();
            const dashboardMap = {
                'product_specialist': '/ps/dashboard.html',
                'district_manager': '/manager/dashboard.html',
                'senior_manager': '/manager/dashboard.html',
                'admin': '/admin/dashboard.html'
            };
            
            const redirectUrl = dashboardMap[session.role] || '/ps/dashboard.html';
            window.location.href = redirectUrl;
        }
    </script>
</body>
</html>
