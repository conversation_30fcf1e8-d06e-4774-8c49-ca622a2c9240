// API module for Market Tracking System - Supabase interactions
import { supabase, getSession, getCurrentUser, isAdmin } from './auth.js';

// Get current user's scope for data filtering
export function getMyScope() {
    const session = getSession();
    if (!session) return null;

    return {
        user_id: session.user_id,
        employee_id: session.employee_id,
        role: session.role,
        direct_manager_id: session.employee?.direct_manager_id,
        area: session.employee?.area,
        line: session.employee?.line,
        governorate: session.employee?.governorate
    };
}

// Get user's direct reports (for managers)
export async function getMyDirectReports() {
    const scope = getMyScope();
    if (!scope || scope.role === 'product_specialist') return [];

    try {
        const { data, error } = await supabase
            .from('employees')
            .select('id, code_number, full_name, position_title')
            .eq('direct_manager_id', scope.employee_id);

        if (error) throw error;
        return data || [];
    } catch (error) {
        console.error('Error fetching direct reports:', error);
        return [];
    }
}

// Get user's team hierarchy (for viewing data)
export async function getMyTeamHierarchy() {
    const scope = getMyScope();
    if (!scope) return [];

    if (scope.role === 'admin') {
        // Admin sees everyone
        const { data, error } = await supabase
            .from('employees')
            .select('id, code_number, full_name, position_title, direct_manager_id');
        
        if (error) throw error;
        return data || [];
    }

    if (scope.role === 'product_specialist') {
        // PS only sees themselves
        return [scope.employee_id];
    }

    // For managers, get all subordinates recursively
    try {
        const subordinates = await getSubordinatesRecursive(scope.employee_id);
        return [scope.employee_id, ...subordinates];
    } catch (error) {
        console.error('Error fetching team hierarchy:', error);
        return [scope.employee_id];
    }
}

// Recursive function to get all subordinates
async function getSubordinatesRecursive(managerId) {
    const { data, error } = await supabase
        .from('employees')
        .select('id')
        .eq('direct_manager_id', managerId);

    if (error) throw error;
    if (!data || data.length === 0) return [];

    const directReports = data.map(emp => emp.id);
    let allSubordinates = [...directReports];

    // Get subordinates of each direct report
    for (const reportId of directReports) {
        const subSubordinates = await getSubordinatesRecursive(reportId);
        allSubordinates = [...allSubordinates, ...subSubordinates];
    }

    return allSubordinates;
}

// CRUD Operations for Pending Items

// Create account
export async function createAccount(accountData) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    const data = {
        ...accountData,
        submitted_by: scope.user_id,
        status: scope.role === 'admin' ? 'approved' : 'pending'
    };

    if (scope.role === 'admin') {
        data.admin_approved_by = scope.user_id;
        data.admin_approved_at = new Date().toISOString();
    }

    const { data: result, error } = await supabase
        .from('accounts')
        .insert(data)
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Create doctor
export async function createDoctor(doctorData) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    const data = {
        ...doctorData,
        submitted_by: scope.user_id,
        status: scope.role === 'admin' ? 'approved' : 'pending'
    };

    if (scope.role === 'admin') {
        data.admin_approved_by = scope.user_id;
        data.admin_approved_at = new Date().toISOString();
    }

    const { data: result, error } = await supabase
        .from('doctors')
        .insert(data)
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Create product
export async function createProduct(productData) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    const data = {
        ...productData,
        submitted_by: scope.user_id,
        status: scope.role === 'admin' ? 'approved' : 'pending'
    };

    if (scope.role === 'admin') {
        data.admin_approved_by = scope.user_id;
        data.admin_approved_at = new Date().toISOString();
    }

    const { data: result, error } = await supabase
        .from('products')
        .insert(data)
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Create case with products
export async function createCase(caseData, products) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    try {
        // Start transaction by creating case first
        const caseRecord = {
            case_date: caseData.case_date,
            doctor_id: caseData.doctor_id,
            account_id: caseData.account_id,
            comments: caseData.comments,
            submitted_by: scope.user_id,
            status: scope.role === 'admin' ? 'approved' : 'pending'
        };

        if (scope.role === 'admin') {
            caseRecord.admin_approved_by = scope.user_id;
            caseRecord.admin_approved_at = new Date().toISOString();
        }

        const { data: newCase, error: caseError } = await supabase
            .from('cases')
            .insert(caseRecord)
            .select()
            .single();

        if (caseError) throw caseError;

        // Create case products
        const caseProducts = products.map(product => ({
            case_id: newCase.id,
            company_id: product.company_id,
            category_id: product.category_id,
            product_id: product.product_id,
            units: product.units
        }));

        const { error: productsError } = await supabase
            .from('case_products')
            .insert(caseProducts);

        if (productsError) throw productsError;

        return newCase;
    } catch (error) {
        console.error('Error creating case:', error);
        throw error;
    }
}

// Update pending item (only by submitter)
export async function updatePendingItem(table, id, data) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    const { data: result, error } = await supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .eq('submitted_by', scope.user_id)
        .eq('status', 'pending')
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Delete pending item (only by submitter)
export async function deletePendingItem(table, id) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id)
        .eq('submitted_by', scope.user_id)
        .eq('status', 'pending');

    if (error) throw error;
}

// Approval functions

// Manager approval (first step)
export async function approveByManager(table, id) {
    const scope = getMyScope();
    if (!scope || !['district_manager', 'senior_manager'].includes(scope.role)) {
        throw new Error('Not authorized to approve');
    }

    const updateData = {
        manager_approved_by: scope.user_id,
        manager_approved_at: new Date().toISOString()
    };

    const { data: result, error } = await supabase
        .from(table)
        .update(updateData)
        .eq('id', id)
        .eq('status', 'pending')
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Admin final approval
export async function approveByAdmin(table, id, editData = null) {
    const scope = getMyScope();
    if (!scope || scope.role !== 'admin') {
        throw new Error('Not authorized for final approval');
    }

    const updateData = {
        status: 'approved',
        admin_approved_by: scope.user_id,
        admin_approved_at: new Date().toISOString(),
        ...editData
    };

    const { data: result, error } = await supabase
        .from(table)
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Reject item
export async function rejectItem(table, id, reason) {
    const scope = getMyScope();
    if (!scope || !['district_manager', 'senior_manager', 'admin'].includes(scope.role)) {
        throw new Error('Not authorized to reject');
    }

    const updateData = {
        status: 'rejected',
        rejection_reason: reason
    };

    const { data: result, error } = await supabase
        .from(table)
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

    if (error) throw error;
    return result;
}

// Query functions

// Get approved accounts (scoped)
export async function getApprovedAccounts(filters = {}) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    let query = supabase
        .from('accounts')
        .select('*')
        .eq('status', 'approved');

    // Apply scope filtering for non-admin users
    if (scope.role !== 'admin') {
        if (scope.governorate) {
            query = query.eq('governorate', scope.governorate);
        }
    }

    // Apply additional filters
    if (filters.governorate) {
        query = query.eq('governorate', filters.governorate);
    }
    if (filters.type) {
        query = query.eq('type', filters.type);
    }
    if (filters.search) {
        query = query.ilike('name', `%${filters.search}%`);
    }

    const { data, error } = await query.order('name');
    if (error) throw error;
    return data || [];
}

// Get approved doctors (scoped)
export async function getApprovedDoctors(filters = {}) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    let query = supabase
        .from('doctors')
        .select(`
            *,
            doctor_accounts (
                account_id,
                accounts (
                    id,
                    name,
                    type,
                    governorate
                )
            )
        `)
        .eq('status', 'approved');

    // Apply additional filters
    if (filters.specialty) {
        query = query.eq('specialty', filters.specialty);
    }
    if (filters.search) {
        query = query.ilike('name', `%${filters.search}%`);
    }

    const { data, error } = await query.order('name');
    if (error) throw error;
    return data || [];
}

// Get approved products (scoped)
export async function getApprovedProducts(filters = {}) {
    let query = supabase
        .from('products')
        .select(`
            *,
            companies (
                id,
                name
            ),
            categories (
                id,
                name
            )
        `)
        .eq('status', 'approved');

    // Apply filters
    if (filters.company_id) {
        query = query.eq('company_id', filters.company_id);
    }
    if (filters.category_id) {
        query = query.eq('category_id', filters.category_id);
    }
    if (filters.search) {
        query = query.ilike('name', `%${filters.search}%`);
    }

    const { data, error } = await query.order('name');
    if (error) throw error;
    return data || [];
}

// Get companies
export async function getCompanies() {
    const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('name');

    if (error) throw error;
    return data || [];
}

// Get categories
export async function getCategories() {
    const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

    if (error) throw error;
    return data || [];
}

// Get approved cases with full details (scoped)
export async function getApprovedCases(filters = {}, limit = 100, offset = 0) {
    const scope = getMyScope();
    if (!scope) throw new Error('Not authenticated');

    let query = supabase
        .from('cases')
        .select(`
            *,
            doctors (
                id,
                name,
                specialty
            ),
            accounts (
                id,
                name,
                type,
                governorate
            ),
            case_products (
                id,
                units,
                companies (
                    id,
                    name
                ),
                categories (
                    id,
                    name
                ),
                products (
                    id,
                    name
                )
            ),
            submitted_by_user:users!submitted_by (
                id,
                username,
                employees (
                    full_name
                )
            )
        `)
        .eq('status', 'approved');

    // Apply scope filtering for non-admin users
    if (scope.role !== 'admin') {
        const teamIds = await getMyTeamHierarchy();
        if (teamIds.length > 0) {
            query = query.in('submitted_by', teamIds);
        }
    }

    // Apply filters
    if (filters.date_from) {
        query = query.gte('case_date', filters.date_from);
    }
    if (filters.date_to) {
        query = query.lte('case_date', filters.date_to);
    }
    if (filters.doctor_id) {
        query = query.eq('doctor_id', filters.doctor_id);
    }
    if (filters.account_id) {
        query = query.eq('account_id', filters.account_id);
    }

    const { data, error } = await query
        .order('case_date', { ascending: false })
        .range(offset, offset + limit - 1);

    if (error) throw error;
    return data || [];
}

// Fuzzy search functions
export async function suggestDoctorByName(name) {
    if (!name || name.length < 2) return [];

    const { data, error } = await supabase
        .from('doctors')
        .select('id, name, specialty')
        .eq('status', 'approved')
        .ilike('name', `%${name}%`)
        .limit(10);

    if (error) throw error;
    return data || [];
}

export async function suggestAccountByName(name) {
    if (!name || name.length < 2) return [];

    const { data, error } = await supabase
        .from('accounts')
        .select('id, name, type, governorate')
        .eq('status', 'approved')
        .ilike('name', `%${name}%`)
        .limit(10);

    if (error) throw error;
    return data || [];
}
