// Authentication module for Market Tracking System
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './config.js';
import { showToast } from './utils.js';

// Initialize Supabase client with error handling
let supabase;

export function initSupabase() {
    if (!supabase) {
        if (typeof window !== 'undefined' && window.supabase && window.supabase.createClient) {
            supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        } else {
            console.error('Supabase client not available. Make sure the Supabase script is loaded.');
            throw new Error('Supabase client not available');
        }
    }
    return supabase;
}

// Export supabase client for other modules
export { supabase };

// Session storage keys
const SESSION_KEY = 'mts_session';
const USER_KEY = 'mts_user';

// Login function
export async function login(username, password) {
    try {
        const supabaseClient = initSupabase();

        // Query users table with plain text password
        const { data: users, error } = await supabaseClient
            .from('users')
            .select(`
                id,
                username,
                role,
                is_active,
                employee_id,
                employees (
                    id,
                    code_number,
                    full_name,
                    position_title,
                    line,
                    area,
                    governorate,
                    direct_manager_id
                )
            `)
            .eq('username', username)
            .eq('password_plain', password)
            .eq('is_active', true)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                throw new Error('Invalid username or password');
            }
            throw error;
        }

        if (!users) {
            throw new Error('Invalid username or password');
        }

        // Store session data
        const sessionData = {
            user_id: users.id,
            username: users.username,
            role: users.role,
            employee_id: users.employee_id,
            employee: users.employees,
            loginTime: new Date().toISOString()
        };

        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
        localStorage.setItem(USER_KEY, JSON.stringify(users));

        return sessionData;
    } catch (error) {
        console.error('Login error:', error);
        throw error;
    }
}

// Logout function
export function logout() {
    localStorage.removeItem(SESSION_KEY);
    localStorage.removeItem(USER_KEY);
    window.location.href = '/login.html';
}

// Get current session
export function getSession() {
    const sessionData = localStorage.getItem(SESSION_KEY);
    return sessionData ? JSON.parse(sessionData) : null;
}

// Get current user
export function getCurrentUser() {
    const userData = localStorage.getItem(USER_KEY);
    return userData ? JSON.parse(userData) : null;
}

// Check if user is authenticated
export function isAuthenticated() {
    return getSession() !== null;
}

// Check if user has specific role
export function hasRole(role) {
    const session = getSession();
    return session && session.role === role;
}

// Check if user has any of the specified roles
export function hasAnyRole(roles) {
    const session = getSession();
    return session && roles.includes(session.role);
}

// Require authentication - redirect to login if not authenticated
export function requireAuth() {
    if (!isAuthenticated()) {
        window.location.href = '/login.html';
        return false;
    }
    return true;
}

// Require specific role - redirect to appropriate dashboard if wrong role
export function requireRole(allowedRoles) {
    if (!requireAuth()) return false;
    
    const session = getSession();
    if (!allowedRoles.includes(session.role)) {
        // Redirect to appropriate dashboard based on role
        const dashboardMap = {
            'product_specialist': '/ps/dashboard.html',
            'district_manager': '/manager/dashboard.html',
            'senior_manager': '/manager/dashboard.html',
            'admin': '/admin/dashboard.html'
        };
        
        const redirectUrl = dashboardMap[session.role] || '/login.html';
        window.location.href = redirectUrl;
        return false;
    }
    return true;
}

// Get user's direct manager ID
export function getDirectManagerId() {
    const session = getSession();
    return session?.employee?.direct_manager_id || null;
}

// Check if user is a manager (DM or SM)
export function isManager() {
    return hasAnyRole(['district_manager', 'senior_manager']);
}

// Check if user is admin
export function isAdmin() {
    return hasRole('admin');
}

// Get role display name
export function getRoleDisplayName(role) {
    const roleNames = {
        'product_specialist': 'Product Specialist',
        'district_manager': 'District Manager',
        'senior_manager': 'Senior Manager',
        'admin': 'Administrator'
    };
    return roleNames[role] || role;
}

// Initialize authentication on page load
export function initAuth() {
    // Check if we're on login page
    if (window.location.pathname.includes('login.html')) {
        // If already authenticated, redirect to appropriate dashboard
        if (isAuthenticated()) {
            const session = getSession();
            const dashboardMap = {
                'product_specialist': '/ps/dashboard.html',
                'district_manager': '/manager/dashboard.html',
                'senior_manager': '/manager/dashboard.html',
                'admin': '/admin/dashboard.html'
            };
            window.location.href = dashboardMap[session.role] || '/ps/dashboard.html';
        }
        return;
    }

    // For all other pages, require authentication
    if (!requireAuth()) return;

    // Update UI with user info
    updateUserUI();
}

// Update UI elements with user information
function updateUserUI() {
    const session = getSession();
    if (!session) return;

    // Update user name in header
    const userNameElements = document.querySelectorAll('.user-name');
    userNameElements.forEach(el => {
        el.textContent = session.employee?.full_name || session.username;
    });

    // Update user role in header
    const userRoleElements = document.querySelectorAll('.user-role');
    userRoleElements.forEach(el => {
        el.textContent = getRoleDisplayName(session.role);
    });

    // Update logout buttons
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            logout();
        });
    });
}

// Export supabase client for use in other modules
export { supabase };
