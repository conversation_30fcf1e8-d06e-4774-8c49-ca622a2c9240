// Charts module for Market Tracking System analytics
import { OUR_COMPANY_NAME } from './config.js';
import { showToast, formatNumber, calculatePercentage } from './utils.js';

// Chart color palette
const CHART_COLORS = {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#4facfe',
    warning: '#43e97b',
    danger: '#fa709a',
    info: '#38f9d7',
    light: '#f8f9fa',
    dark: '#343a40'
};

const GRADIENT_COLORS = [
    'rgba(102, 126, 234, 0.8)',
    'rgba(118, 75, 162, 0.8)',
    'rgba(79, 172, 254, 0.8)',
    'rgba(67, 233, 123, 0.8)',
    'rgba(250, 112, 154, 0.8)',
    'rgba(56, 249, 215, 0.8)',
    'rgba(255, 193, 7, 0.8)',
    'rgba(220, 53, 69, 0.8)'
];

export class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true
                }
            }
        };
    }
    
    // Create company share chart (doughnut)
    createCompanyShareChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        const chartData = this.prepareCompanyShareData(data);
        
        const config = {
            type: 'doughnut',
            data: chartData,
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    legend: {
                        position: 'right',
                        labels: {
                            generateLabels: (chart) => {
                                const data = chart.data;
                                return data.labels.map((label, index) => ({
                                    text: `${label}: ${data.datasets[0].data[index]}%`,
                                    fillStyle: data.datasets[0].backgroundColor[index],
                                    strokeStyle: data.datasets[0].backgroundColor[index],
                                    pointStyle: 'circle'
                                }));
                            }
                        }
                    },
                    tooltip: {
                        ...this.defaultOptions.plugins.tooltip,
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                return `${label}: ${value}%`;
                            }
                        }
                    }
                },
                cutout: '60%',
                ...options
            }
        };
        
        const chart = new Chart(ctx, config);
        this.charts.set(canvasId, chart);
        return chart;
    }
    
    // Create trend line chart
    createTrendChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        const chartData = this.prepareTrendData(data);
        
        const config = {
            type: 'line',
            data: chartData,
            options: {
                ...this.defaultOptions,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(ctx, config);
        this.charts.set(canvasId, chart);
        return chart;
    }
    
    // Create bar chart for comparisons
    createBarChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        const chartData = this.prepareBarData(data);
        
        const config = {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(ctx, config);
        this.charts.set(canvasId, chart);
        return chart;
    }
    
    // Create stacked bar chart
    createStackedBarChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;
        
        const chartData = this.prepareStackedBarData(data);
        
        const config = {
            type: 'bar',
            data: chartData,
            options: {
                ...this.defaultOptions,
                scales: {
                    x: {
                        stacked: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(ctx, config);
        this.charts.set(canvasId, chart);
        return chart;
    }
    
    // Prepare company share data
    prepareCompanyShareData(data) {
        if (!data || data.length === 0) {
            return {
                labels: ['No Data'],
                datasets: [{
                    data: [100],
                    backgroundColor: ['#e9ecef'],
                    borderWidth: 0
                }]
            };
        }
        
        // Calculate total units
        const totalUnits = data.reduce((sum, item) => sum + (item.units || 0), 0);
        
        // Prepare chart data
        const labels = [];
        const values = [];
        const colors = [];
        
        data.forEach((item, index) => {
            const percentage = calculatePercentage(item.units || 0, totalUnits);
            labels.push(item.company_name || 'Unknown');
            values.push(percentage);
            colors.push(GRADIENT_COLORS[index % GRADIENT_COLORS.length]);
        });
        
        return {
            labels,
            datasets: [{
                data: values,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };
    }
    
    // Prepare trend data
    prepareTrendData(data) {
        if (!data || data.length === 0) {
            return {
                labels: ['No Data'],
                datasets: [{
                    label: 'No Data',
                    data: [0],
                    borderColor: CHART_COLORS.light,
                    backgroundColor: 'rgba(233, 236, 239, 0.1)',
                    fill: true
                }]
            };
        }
        
        // Group data by time period and company
        const groupedData = this.groupDataByPeriod(data);
        const labels = Object.keys(groupedData).sort();
        
        // Get unique companies
        const companies = [...new Set(data.map(item => item.company_name))];
        
        const datasets = companies.map((company, index) => ({
            label: company,
            data: labels.map(label => groupedData[label][company] || 0),
            borderColor: GRADIENT_COLORS[index % GRADIENT_COLORS.length],
            backgroundColor: GRADIENT_COLORS[index % GRADIENT_COLORS.length].replace('0.8', '0.1'),
            fill: false,
            tension: 0.4
        }));
        
        return { labels, datasets };
    }
    
    // Prepare bar chart data
    prepareBarData(data) {
        if (!data || data.length === 0) {
            return {
                labels: ['No Data'],
                datasets: [{
                    label: 'No Data',
                    data: [0],
                    backgroundColor: [CHART_COLORS.light]
                }]
            };
        }
        
        const labels = data.map(item => item.label || item.name);
        const values = data.map(item => item.value || item.units || 0);
        const colors = data.map((_, index) => GRADIENT_COLORS[index % GRADIENT_COLORS.length]);
        
        return {
            labels,
            datasets: [{
                label: 'Units',
                data: values,
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('0.8', '1')),
                borderWidth: 1
            }]
        };
    }
    
    // Prepare stacked bar data
    prepareStackedBarData(data) {
        if (!data || data.length === 0) {
            return {
                labels: ['No Data'],
                datasets: [{
                    label: 'No Data',
                    data: [0],
                    backgroundColor: [CHART_COLORS.light]
                }]
            };
        }
        
        // This would need to be customized based on the specific stacked data structure
        return this.prepareBarData(data);
    }
    
    // Group data by time period
    groupDataByPeriod(data) {
        const grouped = {};
        
        data.forEach(item => {
            const period = item.period || item.month || item.date;
            const company = item.company_name || 'Unknown';
            const units = item.units || 0;
            
            if (!grouped[period]) {
                grouped[period] = {};
            }
            
            if (!grouped[period][company]) {
                grouped[period][company] = 0;
            }
            
            grouped[period][company] += units;
        });
        
        return grouped;
    }
    
    // Update chart data
    updateChart(canvasId, newData) {
        const chart = this.charts.get(canvasId);
        if (!chart) return;
        
        // Update data based on chart type
        if (chart.config.type === 'doughnut') {
            const chartData = this.prepareCompanyShareData(newData);
            chart.data = chartData;
        } else if (chart.config.type === 'line') {
            const chartData = this.prepareTrendData(newData);
            chart.data = chartData;
        } else if (chart.config.type === 'bar') {
            const chartData = this.prepareBarData(newData);
            chart.data = chartData;
        }
        
        chart.update();
    }
    
    // Export chart as image
    exportChart(canvasId, filename = 'chart') {
        const chart = this.charts.get(canvasId);
        if (!chart) {
            showToast('Chart not found', 'error');
            return;
        }
        
        try {
            const canvas = chart.canvas;
            const url = canvas.toDataURL('image/png');
            
            const link = document.createElement('a');
            link.download = `${filename}.png`;
            link.href = url;
            link.click();
            
            showToast('Chart exported successfully', 'success');
        } catch (error) {
            console.error('Export error:', error);
            showToast('Error exporting chart', 'error');
        }
    }
    
    // Destroy chart
    destroyChart(canvasId) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.destroy();
            this.charts.delete(canvasId);
        }
    }
    
    // Destroy all charts
    destroyAllCharts() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
    }
    
    // Resize charts (useful for responsive layouts)
    resizeCharts() {
        this.charts.forEach(chart => chart.resize());
    }
    
    // Get chart instance
    getChart(canvasId) {
        return this.charts.get(canvasId);
    }
    
    // Create analytics dashboard
    createAnalyticsDashboard(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        // This would create a complete analytics dashboard with multiple charts
        // Implementation would depend on specific requirements
        
        container.innerHTML = `
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <h6 class="mb-0">Company Market Share</h6>
                            <button class="btn btn-sm btn-outline-primary" onclick="chartManager.exportChart('companyShareChart', 'company_share')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <canvas id="companyShareChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <h6 class="mb-0">Monthly Trends</h6>
                            <button class="btn btn-sm btn-outline-primary" onclick="chartManager.exportChart('trendsChart', 'monthly_trends')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <canvas id="trendsChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Initialize charts with data
        this.createCompanyShareChart('companyShareChart', data.companyShare);
        this.createTrendChart('trendsChart', data.trends);
    }
}

// Global chart manager instance
export const chartManager = new ChartManager();

// Make it available globally for export buttons
window.chartManager = chartManager;
