// Filters module for Market Tracking System
import { debounce } from './utils.js';
import { getCompanies, getCategories, getApprovedProducts, getApprovedAccounts, getApprovedDoctors } from './api.js';

export class FilterManager {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            showDateRange: true,
            showCompany: true,
            showCategory: true,
            showProduct: true,
            showAccount: true,
            showDoctor: true,
            showGovernorate: true,
            showAccountType: true,
            showTextSearch: true,
            ...options
        };
        
        this.filters = {};
        this.callbacks = [];
        this.masterData = {};
        
        this.init();
    }
    
    async init() {
        await this.loadMasterData();
        this.createFilterUI();
        this.setupEventListeners();
    }
    
    async loadMasterData() {
        try {
            const [companies, categories, accounts, doctors] = await Promise.all([
                this.options.showCompany ? getCompanies() : [],
                this.options.showCategory ? getCategories() : [],
                this.options.showAccount ? getApprovedAccounts() : [],
                this.options.showDoctor ? getApprovedDoctors() : []
            ]);
            
            this.masterData = {
                companies,
                categories,
                accounts,
                doctors,
                governorates: [...new Set(accounts.map(a => a.governorate))].filter(Boolean).sort(),
                accountTypes: ['private', 'upa']
            };
        } catch (error) {
            console.error('Error loading master data for filters:', error);
        }
    }
    
    createFilterUI() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="filter-panel">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i>Filters
                            </h6>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" id="clearFiltersBtn">
                                    <i class="fas fa-times me-1"></i>Clear
                                </button>
                                <button class="btn btn-outline-primary" id="applyFiltersBtn">
                                    <i class="fas fa-check me-1"></i>Apply
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            ${this.createFilterControls()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    createFilterControls() {
        let controls = '';
        
        // Date Range Filter
        if (this.options.showDateRange) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Date From</label>
                    <input type="date" class="form-control form-control-sm" id="dateFrom" name="date_from">
                </div>
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Date To</label>
                    <input type="date" class="form-control form-control-sm" id="dateTo" name="date_to">
                </div>
            `;
        }
        
        // Company Filter
        if (this.options.showCompany) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Company</label>
                    <select class="form-select form-select-sm" id="companyFilter" name="company_id">
                        <option value="">All Companies</option>
                        ${this.masterData.companies?.map(c => `<option value="${c.id}">${c.name}</option>`).join('') || ''}
                    </select>
                </div>
            `;
        }
        
        // Category Filter
        if (this.options.showCategory) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Category</label>
                    <select class="form-select form-select-sm" id="categoryFilter" name="category_id">
                        <option value="">All Categories</option>
                        ${this.masterData.categories?.map(c => `<option value="${c.id}">${c.name}</option>`).join('') || ''}
                    </select>
                </div>
            `;
        }
        
        // Product Filter
        if (this.options.showProduct) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Product</label>
                    <select class="form-select form-select-sm" id="productFilter" name="product_id" disabled>
                        <option value="">All Products</option>
                    </select>
                </div>
            `;
        }
        
        // Account Filter
        if (this.options.showAccount) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Account</label>
                    <select class="form-select form-select-sm" id="accountFilter" name="account_id">
                        <option value="">All Accounts</option>
                        ${this.masterData.accounts?.map(a => `<option value="${a.id}">${a.name}</option>`).join('') || ''}
                    </select>
                </div>
            `;
        }
        
        // Doctor Filter
        if (this.options.showDoctor) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Doctor</label>
                    <select class="form-select form-select-sm" id="doctorFilter" name="doctor_id">
                        <option value="">All Doctors</option>
                        ${this.masterData.doctors?.map(d => `<option value="${d.id}">${d.name}</option>`).join('') || ''}
                    </select>
                </div>
            `;
        }
        
        // Governorate Filter
        if (this.options.showGovernorate) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Governorate</label>
                    <select class="form-select form-select-sm" id="governorateFilter" name="governorate">
                        <option value="">All Governorates</option>
                        ${this.masterData.governorates?.map(g => `<option value="${g}">${g}</option>`).join('') || ''}
                    </select>
                </div>
            `;
        }
        
        // Account Type Filter
        if (this.options.showAccountType) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Account Type</label>
                    <select class="form-select form-select-sm" id="accountTypeFilter" name="account_type">
                        <option value="">All Types</option>
                        <option value="private">Private</option>
                        <option value="upa">UPA</option>
                    </select>
                </div>
            `;
        }
        
        // Text Search Filter
        if (this.options.showTextSearch) {
            controls += `
                <div class="col-md-6 col-lg-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control form-control-sm" id="textSearch" name="search" placeholder="Search...">
                </div>
            `;
        }
        
        return controls;
    }
    
    setupEventListeners() {
        // Clear filters button
        const clearBtn = this.container.querySelector('#clearFiltersBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
        
        // Apply filters button
        const applyBtn = this.container.querySelector('#applyFiltersBtn');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }
        
        // Company change - update products
        const companyFilter = this.container.querySelector('#companyFilter');
        const categoryFilter = this.container.querySelector('#categoryFilter');
        const productFilter = this.container.querySelector('#productFilter');
        
        if (companyFilter && productFilter) {
            companyFilter.addEventListener('change', () => {
                this.updateProductFilter();
            });
        }
        
        if (categoryFilter && productFilter) {
            categoryFilter.addEventListener('change', () => {
                this.updateProductFilter();
            });
        }
        
        // Auto-apply filters on change (debounced)
        const autoApplyFilters = debounce(() => {
            this.applyFilters();
        }, 500);
        
        // Add change listeners to all filter controls
        const filterControls = this.container.querySelectorAll('input, select');
        filterControls.forEach(control => {
            control.addEventListener('change', autoApplyFilters);
            if (control.type === 'text') {
                control.addEventListener('input', autoApplyFilters);
            }
        });
    }
    
    async updateProductFilter() {
        const companyFilter = this.container.querySelector('#companyFilter');
        const categoryFilter = this.container.querySelector('#categoryFilter');
        const productFilter = this.container.querySelector('#productFilter');
        
        if (!productFilter) return;
        
        const companyId = companyFilter?.value;
        const categoryId = categoryFilter?.value;
        
        try {
            productFilter.innerHTML = '<option value="">Loading...</option>';
            productFilter.disabled = true;
            
            const filters = {};
            if (companyId) filters.company_id = companyId;
            if (categoryId) filters.category_id = categoryId;
            
            const products = await getApprovedProducts(filters);
            
            productFilter.innerHTML = '<option value="">All Products</option>';
            products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = product.name;
                productFilter.appendChild(option);
            });
            
            productFilter.disabled = false;
        } catch (error) {
            console.error('Error updating product filter:', error);
            productFilter.innerHTML = '<option value="">Error loading products</option>';
        }
    }
    
    getFilters() {
        const filters = {};
        const filterControls = this.container.querySelectorAll('input, select');
        
        filterControls.forEach(control => {
            const name = control.name;
            const value = control.value;
            
            if (name && value) {
                filters[name] = value;
            }
        });
        
        return filters;
    }
    
    setFilters(filters) {
        Object.entries(filters).forEach(([key, value]) => {
            const control = this.container.querySelector(`[name="${key}"]`);
            if (control) {
                control.value = value;
            }
        });
        
        // Update dependent filters
        this.updateProductFilter();
    }
    
    clearFilters() {
        const filterControls = this.container.querySelectorAll('input, select');
        filterControls.forEach(control => {
            if (control.type === 'date' || control.type === 'text') {
                control.value = '';
            } else if (control.tagName === 'SELECT') {
                control.selectedIndex = 0;
            }
        });
        
        // Update dependent filters
        this.updateProductFilter();
        
        // Apply cleared filters
        this.applyFilters();
    }
    
    applyFilters() {
        this.filters = this.getFilters();
        this.notifyCallbacks();
    }
    
    onFiltersChange(callback) {
        this.callbacks.push(callback);
    }
    
    notifyCallbacks() {
        this.callbacks.forEach(callback => {
            try {
                callback(this.filters);
            } catch (error) {
                console.error('Error in filter callback:', error);
            }
        });
    }
    
    // Preset filter configurations
    setDateRange(days) {
        const dateTo = new Date();
        const dateFrom = new Date();
        dateFrom.setDate(dateTo.getDate() - days);
        
        const dateFromControl = this.container.querySelector('#dateFrom');
        const dateToControl = this.container.querySelector('#dateTo');
        
        if (dateFromControl) {
            dateFromControl.value = dateFrom.toISOString().split('T')[0];
        }
        if (dateToControl) {
            dateToControl.value = dateTo.toISOString().split('T')[0];
        }
        
        this.applyFilters();
    }
    
    setCurrentMonth() {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        const dateFromControl = this.container.querySelector('#dateFrom');
        const dateToControl = this.container.querySelector('#dateTo');
        
        if (dateFromControl) {
            dateFromControl.value = firstDay.toISOString().split('T')[0];
        }
        if (dateToControl) {
            dateToControl.value = lastDay.toISOString().split('T')[0];
        }
        
        this.applyFilters();
    }
    
    setCurrentYear() {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), 0, 1);
        const lastDay = new Date(now.getFullYear(), 11, 31);
        
        const dateFromControl = this.container.querySelector('#dateFrom');
        const dateToControl = this.container.querySelector('#dateTo');
        
        if (dateFromControl) {
            dateFromControl.value = firstDay.toISOString().split('T')[0];
        }
        if (dateToControl) {
            dateToControl.value = lastDay.toISOString().split('T')[0];
        }
        
        this.applyFilters();
    }
    
    // Quick filter buttons
    createQuickFilters() {
        const quickFiltersHTML = `
            <div class="quick-filters mb-3">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="filterManager.setDateRange(7)">Last 7 Days</button>
                    <button type="button" class="btn btn-outline-primary" onclick="filterManager.setDateRange(30)">Last 30 Days</button>
                    <button type="button" class="btn btn-outline-primary" onclick="filterManager.setCurrentMonth()">This Month</button>
                    <button type="button" class="btn btn-outline-primary" onclick="filterManager.setCurrentYear()">This Year</button>
                </div>
            </div>
        `;
        
        this.container.insertAdjacentHTML('afterbegin', quickFiltersHTML);
    }
}

// Export for global access
export let filterManager = null;

export function initializeFilters(containerId, options = {}) {
    filterManager = new FilterManager(containerId, options);
    window.filterManager = filterManager; // Make available globally
    return filterManager;
}
