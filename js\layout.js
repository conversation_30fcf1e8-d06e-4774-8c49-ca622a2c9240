// Layout components and navigation for Market Tracking System
import { getSession, logout, getRoleDisplayName } from './auth.js';

// Initialize layout components
export function initLayout() {
    createHeader();
    createSidebar();
    initSidebarToggle();
    updateUserInfo();
}

// Create main header
function createHeader() {
    const header = document.createElement('header');
    header.className = 'main-header d-flex align-items-center justify-content-between px-4';
    header.innerHTML = `
        <div class="d-flex align-items-center">
            <button class="btn btn-link text-white p-0 me-3" id="sidebarToggle">
                <i class="fas fa-bars fa-lg"></i>
            </button>
            <a href="/" class="header-brand">
                <img src="/assets/logo.svg" alt="Logo" class="header-logo">
                Market Tracking System
            </a>
        </div>
        <div class="user-info">
            <div class="dropdown">
                <button class="btn btn-link text-white dropdown-toggle p-0" type="button" data-bs-toggle="dropdown">
                    <div class="user-avatar me-2">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="d-none d-md-block text-start">
                        <div class="user-name fw-semibold"></div>
                        <small class="user-role opacity-75"></small>
                    </div>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><h6 class="dropdown-header">
                        <div class="user-name"></div>
                        <small class="user-role text-muted"></small>
                    </h6></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" id="profileBtn">
                        <i class="fas fa-user me-2"></i>Profile
                    </a></li>
                    <li><a class="dropdown-item" href="#" id="settingsBtn">
                        <i class="fas fa-cog me-2"></i>Settings
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item logout-btn" href="#">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a></li>
                </ul>
            </div>
        </div>
    `;
    
    document.body.insertBefore(header, document.body.firstChild);
}

// Create sidebar navigation
function createSidebar() {
    const session = getSession();
    if (!session) return;
    
    const sidebar = document.createElement('nav');
    sidebar.className = 'sidebar';
    sidebar.id = 'sidebar';
    
    const sidebarContent = getSidebarContent(session.role);
    sidebar.innerHTML = sidebarContent;
    
    document.body.appendChild(sidebar);
    
    // Add click handlers for navigation
    const navLinks = sidebar.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            if (link.getAttribute('href') === '#') {
                e.preventDefault();
                return;
            }
            
            // Update active state
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
        });
    });
}

// Get sidebar content based on role
function getSidebarContent(role) {
    const commonHeader = `
        <div class="sidebar-header">
            <a href="/" class="sidebar-brand">
                <img src="/assets/logo.svg" alt="Logo" class="sidebar-logo">
                MTS
            </a>
        </div>
    `;
    
    let navigation = '';
    
    switch (role) {
        case 'product_specialist':
            navigation = `
                <div class="sidebar-nav">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/dashboard.html">
                                <i class="fas fa-tachometer-alt nav-icon"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/add-case.html">
                                <i class="fas fa-plus-circle nav-icon"></i>
                                Add Case
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/cases.html">
                                <i class="fas fa-clipboard-list nav-icon"></i>
                                Cases
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/add-doctor.html">
                                <i class="fas fa-user-md nav-icon"></i>
                                Add Doctor
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/add-account.html">
                                <i class="fas fa-hospital nav-icon"></i>
                                Add Account
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/add-product.html">
                                <i class="fas fa-box nav-icon"></i>
                                Add Product
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/tables.html">
                                <i class="fas fa-table nav-icon"></i>
                                Data Tables
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/ps/notifications.html">
                                <i class="fas fa-bell nav-icon"></i>
                                Notifications
                                <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                            </a>
                        </li>
                    </ul>
                </div>
            `;
            break;
            
        case 'district_manager':
        case 'senior_manager':
            navigation = `
                <div class="sidebar-nav">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/dashboard.html">
                                <i class="fas fa-tachometer-alt nav-icon"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/approvals.html">
                                <i class="fas fa-check-circle nav-icon"></i>
                                Approvals
                                <span class="notification-badge" id="approvalBadge" style="display: none;">0</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/add-case.html">
                                <i class="fas fa-plus-circle nav-icon"></i>
                                Add Case
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/cases.html">
                                <i class="fas fa-clipboard-list nav-icon"></i>
                                Cases
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/add-doctor.html">
                                <i class="fas fa-user-md nav-icon"></i>
                                Add Doctor
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/add-account.html">
                                <i class="fas fa-hospital nav-icon"></i>
                                Add Account
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/add-product.html">
                                <i class="fas fa-box nav-icon"></i>
                                Add Product
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/tables.html">
                                <i class="fas fa-table nav-icon"></i>
                                Data Tables
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manager/notifications.html">
                                <i class="fas fa-bell nav-icon"></i>
                                Notifications
                                <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                            </a>
                        </li>
                    </ul>
                </div>
            `;
            break;
            
        case 'admin':
            navigation = `
                <div class="sidebar-nav">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/dashboard.html">
                                <i class="fas fa-tachometer-alt nav-icon"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/approvals.html">
                                <i class="fas fa-check-circle nav-icon"></i>
                                Approvals
                                <span class="notification-badge" id="approvalBadge" style="display: none;">0</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/users.html">
                                <i class="fas fa-users nav-icon"></i>
                                User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/master-data.html">
                                <i class="fas fa-database nav-icon"></i>
                                Master Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/bulk-upload.html">
                                <i class="fas fa-upload nav-icon"></i>
                                Bulk Upload
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/cases.html">
                                <i class="fas fa-clipboard-list nav-icon"></i>
                                All Cases
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/tables.html">
                                <i class="fas fa-table nav-icon"></i>
                                Data Tables
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/notifications.html">
                                <i class="fas fa-bell nav-icon"></i>
                                Notifications
                                <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                            </a>
                        </li>
                    </ul>
                </div>
            `;
            break;
    }
    
    return commonHeader + navigation;
}

// Initialize sidebar toggle functionality
function initSidebarToggle() {
    const toggleBtn = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const header = document.querySelector('.main-header');
    const mainContent = document.querySelector('.main-content');
    
    if (toggleBtn && sidebar) {
        toggleBtn.addEventListener('click', () => {
            sidebar.classList.toggle('show');
            header?.classList.toggle('sidebar-open');
            mainContent?.classList.toggle('sidebar-open');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggleBtn.contains(e.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                header?.classList.remove('sidebar-open');
                mainContent?.classList.remove('sidebar-content');
            }
        });
    }
}

// Update user information in the UI
function updateUserInfo() {
    const session = getSession();
    if (!session) return;
    
    const userNameElements = document.querySelectorAll('.user-name');
    const userRoleElements = document.querySelectorAll('.user-role');
    
    userNameElements.forEach(el => {
        el.textContent = session.employee?.full_name || session.username;
    });
    
    userRoleElements.forEach(el => {
        el.textContent = getRoleDisplayName(session.role);
    });
    
    // Add logout functionality
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            logout();
        });
    });
}

// Set active navigation item based on current page
export function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// Update notification badges
export function updateNotificationBadges(notifications = 0, approvals = 0) {
    const notificationBadge = document.getElementById('notificationBadge');
    const approvalBadge = document.getElementById('approvalBadge');
    
    if (notificationBadge) {
        if (notifications > 0) {
            notificationBadge.textContent = notifications > 99 ? '99+' : notifications;
            notificationBadge.style.display = 'flex';
        } else {
            notificationBadge.style.display = 'none';
        }
    }
    
    if (approvalBadge) {
        if (approvals > 0) {
            approvalBadge.textContent = approvals > 99 ? '99+' : approvals;
            approvalBadge.style.display = 'flex';
        } else {
            approvalBadge.style.display = 'none';
        }
    }
}
