// Smart tables module with infinite scroll, filtering, and export
import { showToast, formatDate, formatDateTime, debounce } from './utils.js';

export class SmartTable {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            pageSize: 50,
            stickyHeader: true,
            infiniteScroll: true,
            exportable: true,
            filterable: true,
            columnToggle: true,
            ...options
        };
        
        this.data = [];
        this.filteredData = [];
        this.displayedData = [];
        this.currentPage = 0;
        this.loading = false;
        this.filters = {};
        this.sortColumn = null;
        this.sortDirection = 'asc';
        
        this.init();
    }
    
    init() {
        this.createTableStructure();
        this.setupEventListeners();
    }
    
    createTableStructure() {
        this.container.innerHTML = `
            <div class="smart-table-wrapper">
                ${this.options.filterable ? this.createFiltersHTML() : ''}
                ${this.options.columnToggle ? this.createColumnToggleHTML() : ''}
                <div class="table-controls mb-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center gap-2">
                                <label class="form-label mb-0">Show:</label>
                                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-muted">entries</span>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            ${this.options.exportable ? `
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary btn-sm" id="exportVisibleBtn">
                                        <i class="fas fa-download me-1"></i>Export Visible
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" id="exportAllBtn">
                                        <i class="fas fa-file-excel me-1"></i>Export All
                                    </button>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead id="tableHead"></thead>
                        <tbody id="tableBody">
                            <tr>
                                <td colspan="100%" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="table-info mt-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted" id="tableInfo">
                                Showing 0 of 0 entries
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <div id="loadMoreContainer" class="d-none">
                                <button class="btn btn-outline-primary btn-sm" id="loadMoreBtn">
                                    Load More
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    createFiltersHTML() {
        return `
            <div class="table-filters mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <input type="text" class="form-control form-control-sm" 
                                       id="globalSearch" placeholder="Search all columns...">
                            </div>
                            <div class="col-md-8 mb-2">
                                <div id="columnFilters" class="d-flex flex-wrap gap-2">
                                    <!-- Column-specific filters will be added here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    createColumnToggleHTML() {
        return `
            <div class="column-toggle mb-3">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                            type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-columns me-1"></i>Columns
                    </button>
                    <div class="dropdown-menu" id="columnToggleMenu">
                        <!-- Column toggles will be added here -->
                    </div>
                </div>
            </div>
        `;
    }
    
    setupEventListeners() {
        // Global search
        if (this.options.filterable) {
            const globalSearch = this.container.querySelector('#globalSearch');
            if (globalSearch) {
                const debouncedSearch = debounce((query) => {
                    this.applyGlobalFilter(query);
                }, 300);
                
                globalSearch.addEventListener('input', (e) => {
                    debouncedSearch(e.target.value);
                });
            }
        }
        
        // Page size change
        const pageSizeSelect = this.container.querySelector('#pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.options.pageSize = parseInt(e.target.value);
                this.refresh();
            });
        }
        
        // Export buttons
        if (this.options.exportable) {
            const exportVisibleBtn = this.container.querySelector('#exportVisibleBtn');
            const exportAllBtn = this.container.querySelector('#exportAllBtn');
            
            if (exportVisibleBtn) {
                exportVisibleBtn.addEventListener('click', () => {
                    this.exportToExcel(this.displayedData, 'visible_data');
                });
            }
            
            if (exportAllBtn) {
                exportAllBtn.addEventListener('click', () => {
                    this.exportToExcel(this.filteredData, 'all_data');
                });
            }
        }
        
        // Load more button
        const loadMoreBtn = this.container.querySelector('#loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMore();
            });
        }
        
        // Infinite scroll
        if (this.options.infiniteScroll) {
            const tableWrapper = this.container.querySelector('.table-responsive');
            if (tableWrapper) {
                tableWrapper.addEventListener('scroll', debounce(() => {
                    if (tableWrapper.scrollTop + tableWrapper.clientHeight >= 
                        tableWrapper.scrollHeight - 100) {
                        this.loadMore();
                    }
                }, 100));
            }
        }
    }
    
    setColumns(columns) {
        this.columns = columns;
        this.createTableHeader();
        this.createColumnFilters();
        this.createColumnToggle();
    }
    
    createTableHeader() {
        const thead = this.container.querySelector('#tableHead');
        const headerRow = document.createElement('tr');
        
        this.columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column.title;
            th.style.cursor = column.sortable !== false ? 'pointer' : 'default';
            
            if (column.sortable !== false) {
                th.addEventListener('click', () => {
                    this.sort(column.key);
                });
            }
            
            if (column.width) {
                th.style.width = column.width;
            }
            
            headerRow.appendChild(th);
        });
        
        thead.innerHTML = '';
        thead.appendChild(headerRow);
        
        // Make header sticky if option is enabled
        if (this.options.stickyHeader) {
            thead.style.position = 'sticky';
            thead.style.top = '0';
            thead.style.zIndex = '10';
        }
    }
    
    createColumnFilters() {
        if (!this.options.filterable) return;
        
        const filtersContainer = this.container.querySelector('#columnFilters');
        if (!filtersContainer) return;
        
        filtersContainer.innerHTML = '';
        
        this.columns.forEach(column => {
            if (column.filterable === false) return;
            
            const filterDiv = document.createElement('div');
            filterDiv.className = 'filter-item';
            
            if (column.filterType === 'select') {
                const select = document.createElement('select');
                select.className = 'form-select form-select-sm';
                select.style.width = '150px';
                
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = `All ${column.title}`;
                select.appendChild(defaultOption);
                
                // Populate options would be done when data is loaded
                
                select.addEventListener('change', (e) => {
                    this.applyColumnFilter(column.key, e.target.value);
                });
                
                filterDiv.appendChild(select);
            } else {
                const input = document.createElement('input');
                input.type = column.filterType || 'text';
                input.className = 'form-control form-control-sm';
                input.placeholder = `Filter ${column.title}...`;
                input.style.width = '150px';
                
                const debouncedFilter = debounce((value) => {
                    this.applyColumnFilter(column.key, value);
                }, 300);
                
                input.addEventListener('input', (e) => {
                    debouncedFilter(e.target.value);
                });
                
                filterDiv.appendChild(input);
            }
            
            filtersContainer.appendChild(filterDiv);
        });
    }
    
    createColumnToggle() {
        if (!this.options.columnToggle) return;
        
        const toggleMenu = this.container.querySelector('#columnToggleMenu');
        if (!toggleMenu) return;
        
        toggleMenu.innerHTML = '';
        
        this.columns.forEach((column, index) => {
            const item = document.createElement('div');
            item.className = 'dropdown-item-text';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input me-2';
            checkbox.checked = column.visible !== false;
            checkbox.id = `col-toggle-${index}`;
            
            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.htmlFor = `col-toggle-${index}`;
            label.textContent = column.title;
            
            checkbox.addEventListener('change', (e) => {
                this.toggleColumn(index, e.target.checked);
            });
            
            item.appendChild(checkbox);
            item.appendChild(label);
            toggleMenu.appendChild(item);
        });
    }
    
    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.currentPage = 0;
        this.displayedData = [];
        this.refresh();
    }
    
    refresh() {
        this.applyFilters();
        this.applySorting();
        this.renderTable();
        this.updateInfo();
    }
    
    applyFilters() {
        this.filteredData = this.data.filter(row => {
            // Apply global filter
            if (this.globalFilter) {
                const searchText = this.globalFilter.toLowerCase();
                const rowText = Object.values(row).join(' ').toLowerCase();
                if (!rowText.includes(searchText)) return false;
            }
            
            // Apply column filters
            for (const [key, value] of Object.entries(this.filters)) {
                if (value && row[key] && !row[key].toString().toLowerCase().includes(value.toLowerCase())) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.currentPage = 0;
        this.displayedData = [];
    }
    
    applySorting() {
        if (!this.sortColumn) return;
        
        this.filteredData.sort((a, b) => {
            const aVal = a[this.sortColumn];
            const bVal = b[this.sortColumn];
            
            let comparison = 0;
            if (aVal < bVal) comparison = -1;
            if (aVal > bVal) comparison = 1;
            
            return this.sortDirection === 'desc' ? -comparison : comparison;
        });
    }
    
    renderTable() {
        const tbody = this.container.querySelector('#tableBody');
        const startIndex = this.currentPage * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        if (this.currentPage === 0) {
            tbody.innerHTML = '';
            this.displayedData = [];
        }
        
        if (pageData.length === 0 && this.currentPage === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="${this.columns.length}" class="text-center py-4 text-muted">
                        No data available
                    </td>
                </tr>
            `;
            return;
        }
        
        pageData.forEach(row => {
            const tr = document.createElement('tr');
            
            this.columns.forEach(column => {
                const td = document.createElement('td');
                let value = row[column.key];
                
                if (column.render) {
                    value = column.render(value, row);
                } else if (column.type === 'date') {
                    value = formatDate(value);
                } else if (column.type === 'datetime') {
                    value = formatDateTime(value);
                } else if (column.type === 'number') {
                    value = value ? value.toLocaleString() : '0';
                }
                
                if (typeof value === 'string') {
                    td.innerHTML = value;
                } else {
                    td.textContent = value || '';
                }
                
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        this.displayedData.push(...pageData);
        this.updateLoadMoreButton();
    }
    
    loadMore() {
        if (this.loading) return;
        
        const hasMore = (this.currentPage + 1) * this.options.pageSize < this.filteredData.length;
        if (!hasMore) return;
        
        this.loading = true;
        this.currentPage++;
        
        setTimeout(() => {
            this.renderTable();
            this.updateInfo();
            this.loading = false;
        }, 100);
    }
    
    updateLoadMoreButton() {
        const loadMoreContainer = this.container.querySelector('#loadMoreContainer');
        const hasMore = (this.currentPage + 1) * this.options.pageSize < this.filteredData.length;
        
        if (hasMore && this.options.infiniteScroll) {
            loadMoreContainer.classList.remove('d-none');
        } else {
            loadMoreContainer.classList.add('d-none');
        }
    }
    
    updateInfo() {
        const info = this.container.querySelector('#tableInfo');
        const showing = this.displayedData.length;
        const total = this.filteredData.length;
        const totalAll = this.data.length;
        
        let text = `Showing ${showing} of ${total} entries`;
        if (total !== totalAll) {
            text += ` (filtered from ${totalAll} total entries)`;
        }
        
        info.textContent = text;
    }
    
    applyGlobalFilter(query) {
        this.globalFilter = query;
        this.refresh();
    }
    
    applyColumnFilter(column, value) {
        if (value) {
            this.filters[column] = value;
        } else {
            delete this.filters[column];
        }
        this.refresh();
    }
    
    sort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.refresh();
        this.updateSortIndicators();
    }
    
    updateSortIndicators() {
        // Remove existing sort indicators
        this.container.querySelectorAll('th').forEach(th => {
            th.classList.remove('sorted-asc', 'sorted-desc');
        });
        
        // Add sort indicator to current column
        if (this.sortColumn) {
            const columnIndex = this.columns.findIndex(col => col.key === this.sortColumn);
            if (columnIndex >= 0) {
                const th = this.container.querySelectorAll('th')[columnIndex];
                th.classList.add(`sorted-${this.sortDirection}`);
            }
        }
    }
    
    toggleColumn(index, visible) {
        this.columns[index].visible = visible;
        
        // Update table display
        const ths = this.container.querySelectorAll('th');
        const tds = this.container.querySelectorAll('td:nth-child(' + (index + 1) + ')');
        
        if (visible) {
            ths[index].style.display = '';
            tds.forEach(td => td.style.display = '');
        } else {
            ths[index].style.display = 'none';
            tds.forEach(td => td.style.display = 'none');
        }
    }
    
    async exportToExcel(data, filename) {
        try {
            // This would require a library like SheetJS
            showToast('Excel export functionality requires SheetJS library', 'info');
        } catch (error) {
            console.error('Export error:', error);
            showToast('Error exporting data', 'error');
        }
    }
}
