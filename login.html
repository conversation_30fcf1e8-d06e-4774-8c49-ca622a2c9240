<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Market Tracking System</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center gradient-primary">
                <div class="text-center text-white">
                    <img src="/assets/main.svg" alt="Company Logo" class="login-logo mb-4" style="max-width: 300px; height: auto;">
                    <h1 class="display-4 fw-bold mb-3">Market Tracking System</h1>
                    <p class="lead mb-0">Comprehensive surgical case tracking and analytics platform</p>
                    <div class="mt-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="feature-icon mb-2">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                                <h6>Analytics</h6>
                            </div>
                            <div class="col-4">
                                <div class="feature-icon mb-2">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                                <h6>Team Management</h6>
                            </div>
                            <div class="col-4">
                                <div class="feature-icon mb-2">
                                    <i class="fas fa-clipboard-check fa-2x"></i>
                                </div>
                                <h6>Case Tracking</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container">
                    <div class="card shadow-lg border-0" style="max-width: 400px; width: 100%;">
                        <div class="card-body p-5">
                            <!-- Mobile Logo -->
                            <div class="text-center d-lg-none mb-4">
                                <img src="/assets/main.svg" alt="Company Logo" style="max-width: 150px; height: auto;">
                            </div>
                            
                            <div class="text-center mb-4">
                                <h2 class="text-gradient fw-bold">Welcome Back</h2>
                                <p class="text-muted">Sign in to your account</p>
                            </div>
                            
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <div class="invalid-feedback">
                                        Please enter your username.
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="invalid-feedback">
                                        Please enter your password.
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                        Sign In
                                    </button>
                                </div>
                            </form>
                            
                            <div class="text-center mt-4">
                                <small class="text-muted">
                                    Having trouble? Contact your system administrator.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { login, initAuth } from '/js/auth.js';
        import { showToast, validateForm, showLoading, hideLoading } from '/js/utils.js';
        
        // Initialize authentication
        initAuth();
        
        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const form = e.target;
            const loginBtn = document.getElementById('loginBtn');
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            try {
                showLoading(loginBtn);
                
                const session = await login(username, password);
                
                showToast('Login successful! Redirecting...', 'success');
                
                // Redirect based on role
                const dashboardMap = {
                    'product_specialist': '/ps/dashboard.html',
                    'district_manager': '/manager/dashboard.html',
                    'senior_manager': '/manager/dashboard.html',
                    'admin': '/admin/dashboard.html'
                };
                
                const redirectUrl = dashboardMap[session.role] || '/ps/dashboard.html';
                
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1000);
                
            } catch (error) {
                console.error('Login error:', error);
                showToast(error.message || 'Login failed. Please try again.', 'error');
            } finally {
                hideLoading(loginBtn);
            }
        });
        
        // Handle Enter key in password field
        document.getElementById('password').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
    
    <style>
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-logo {
            filter: brightness(0) invert(1);
        }
        
        .feature-icon {
            opacity: 0.8;
        }
        
        .login-form-container {
            width: 100%;
            max-width: 400px;
            padding: 20px;
        }
        
        @media (max-width: 991px) {
            .login-page {
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            }
        }
    </style>
</body>
</html>
