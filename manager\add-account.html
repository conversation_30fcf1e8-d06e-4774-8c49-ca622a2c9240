<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Account - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Add New Account</h1>
                            <p class="text-muted mb-0">Add a new hospital or clinic to the system</p>
                        </div>
                        <div>
                            <a href="/manager/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Account Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Account Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addAccountForm">
                                <!-- Account Name -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="accountName" class="form-label">Account Name *</label>
                                        <input type="text" class="form-control" id="accountName" name="name" required 
                                               placeholder="Enter hospital/clinic name">
                                        <div class="invalid-feedback">
                                            Please provide the account name.
                                        </div>
                                        <div id="duplicateWarning" class="alert alert-warning mt-2 d-none">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Possible duplicate found:</strong>
                                            <div id="duplicateList"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Account Type and Governorate -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="accountType" class="form-label">Account Type *</label>
                                        <select class="form-select" id="accountType" name="type" required>
                                            <option value="">Select Type</option>
                                            <option value="private">Private</option>
                                            <option value="upa">UPA (University/Public)</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select an account type.
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="accountGovernorate" class="form-label">Governorate *</label>
                                        <select class="form-select" id="accountGovernorate" name="governorate" required>
                                            <option value="">Select Governorate</option>
                                            <option value="Cairo">Cairo</option>
                                            <option value="Giza">Giza</option>
                                            <option value="Alexandria">Alexandria</option>
                                            <option value="Qalyubia">Qalyubia</option>
                                            <option value="Port Said">Port Said</option>
                                            <option value="Suez">Suez</option>
                                            <option value="Luxor">Luxor</option>
                                            <option value="Aswan">Aswan</option>
                                            <option value="Asyut">Asyut</option>
                                            <option value="Beheira">Beheira</option>
                                            <option value="Beni Suef">Beni Suef</option>
                                            <option value="Dakahlia">Dakahlia</option>
                                            <option value="Damietta">Damietta</option>
                                            <option value="Fayyum">Fayyum</option>
                                            <option value="Gharbia">Gharbia</option>
                                            <option value="Ismailia">Ismailia</option>
                                            <option value="Kafr el-Sheikh">Kafr el-Sheikh</option>
                                            <option value="Matrouh">Matrouh</option>
                                            <option value="Minya">Minya</option>
                                            <option value="Monufia">Monufia</option>
                                            <option value="New Valley">New Valley</option>
                                            <option value="North Sinai">North Sinai</option>
                                            <option value="Qena">Qena</option>
                                            <option value="Red Sea">Red Sea</option>
                                            <option value="Sharqia">Sharqia</option>
                                            <option value="Sohag">Sohag</option>
                                            <option value="South Sinai">South Sinai</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a governorate.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Account
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/manager/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            suggestAccountByName,
            createAccount 
        } from '/js/api.js';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize form
            initForm();
        });
        
        // Initialize form functionality
        function initForm() {
            // Account name duplicate detection
            const accountNameInput = document.getElementById('accountName');
            const accountTypeSelect = document.getElementById('accountType');
            const accountGovernorateSelect = document.getElementById('accountGovernorate');
            
            const debouncedDuplicateCheck = debounce(async () => {
                const name = accountNameInput.value.trim();
                const type = accountTypeSelect.value;
                const governorate = accountGovernorateSelect.value;
                
                if (name.length < 3) {
                    document.getElementById('duplicateWarning').classList.add('d-none');
                    return;
                }
                
                try {
                    const accounts = await suggestAccountByName(name);
                    
                    // Filter by type and governorate if selected
                    let filteredAccounts = accounts;
                    if (type) {
                        filteredAccounts = filteredAccounts.filter(acc => acc.type === type);
                    }
                    if (governorate) {
                        filteredAccounts = filteredAccounts.filter(acc => acc.governorate === governorate);
                    }
                    
                    if (filteredAccounts.length > 0) {
                        displayDuplicateWarning(filteredAccounts);
                    } else {
                        document.getElementById('duplicateWarning').classList.add('d-none');
                    }
                } catch (error) {
                    console.error('Error checking for duplicates:', error);
                }
            }, 500);
            
            // Check for duplicates when name, type, or governorate changes
            accountNameInput.addEventListener('input', debouncedDuplicateCheck);
            accountTypeSelect.addEventListener('change', debouncedDuplicateCheck);
            accountGovernorateSelect.addEventListener('change', debouncedDuplicateCheck);
            
            // Form submission
            document.getElementById('addAccountForm').addEventListener('submit', handleSubmit);
        }
        
        // Display duplicate warning
        function displayDuplicateWarning(accounts) {
            const duplicateList = document.getElementById('duplicateList');
            duplicateList.innerHTML = accounts.map(account => `
                <div class="mt-1">
                    <strong>${account.name}</strong> - ${account.type.toUpperCase()} - ${account.governorate}
                </div>
            `).join('');
            
            document.getElementById('duplicateWarning').classList.remove('d-none');
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            try {
                showLoading(submitBtn);
                
                const accountData = {
                    name: document.getElementById('accountName').value.trim(),
                    type: document.getElementById('accountType').value,
                    governorate: document.getElementById('accountGovernorate').value
                };
                
                await createAccount(accountData);
                
                showToast('Account submitted successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/manager/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting account:', error);
                showToast(error.message || 'Error submitting account', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
    </script>
</body>
</html>
