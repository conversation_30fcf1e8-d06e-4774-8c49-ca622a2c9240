<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Case - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Add New Case</h1>
                            <p class="text-muted mb-0">Record a new surgical case</p>
                        </div>
                        <div>
                            <a href="/manager/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Case Form -->
            <div class="row">
                <div class="col-xl-10 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Case Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addCaseForm">
                                <!-- Case Date -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="caseDate" class="form-label">Case Date *</label>
                                        <input type="date" class="form-control" id="caseDate" name="case_date" required>
                                        <div class="invalid-feedback">
                                            Please select the case date.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Doctor Selection -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="doctorSearch" class="form-label">Doctor *</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="doctorSearch" 
                                                   placeholder="Search for doctor..." autocomplete="off" required>
                                            <input type="hidden" id="selectedDoctorId" name="doctor_id">
                                            <div id="doctorSuggestions" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                        </div>
                                        <div class="invalid-feedback">
                                            Please select a doctor.
                                        </div>
                                        <div id="selectedDoctor" class="mt-2 d-none">
                                            <div class="alert alert-info py-2">
                                                <strong>Selected:</strong> <span id="selectedDoctorName"></span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="clearDoctor">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="accountSearch" class="form-label">Account *</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="accountSearch" 
                                                   placeholder="Search for account..." autocomplete="off" required>
                                            <input type="hidden" id="selectedAccountId" name="account_id">
                                            <div id="accountSuggestions" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                        </div>
                                        <div class="invalid-feedback">
                                            Please select an account.
                                        </div>
                                        <div id="selectedAccount" class="mt-2 d-none">
                                            <div class="alert alert-info py-2">
                                                <strong>Selected:</strong> <span id="selectedAccountName"></span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="clearAccount">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Products Section -->
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Products Used</h6>
                                        <button type="button" class="btn btn-sm btn-primary" id="addProductBtn">
                                            <i class="fas fa-plus me-1"></i>Add Product
                                        </button>
                                    </div>
                                    
                                    <div id="productsContainer">
                                        <!-- Product rows will be added here -->
                                    </div>
                                    
                                    <div class="text-muted small mt-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        You can add up to 3 products per case. At least one product is required.
                                    </div>
                                </div>
                                
                                <!-- Comments -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <label for="caseComments" class="form-label">Comments</label>
                                        <textarea class="form-control" id="caseComments" name="comments" rows="3" 
                                                  placeholder="Add any additional comments about this case..."></textarea>
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Case
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/manager/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Product Row Template -->
    <template id="productRowTemplate">
        <div class="product-row card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Product <span class="product-number"></span></h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-product-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Company *</label>
                        <select class="form-select company-select" required>
                            <option value="">Select Company</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Category</label>
                        <select class="form-select category-select">
                            <option value="">Select Category</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Product *</label>
                        <select class="form-select product-select" required>
                            <option value="">Select Product</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-4">
                        <label class="form-label">Units *</label>
                        <input type="number" class="form-control units-input" min="1" value="1" required>
                    </div>
                </div>
            </div>
        </div>
    </template>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            suggestDoctorByName, 
            suggestAccountByName,
            getCompanies,
            getCategories,
            getApprovedProducts,
            createCase 
        } from '/js/api.js';
        
        let productCount = 0;
        let companies = [];
        let categories = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Set default date to today
            document.getElementById('caseDate').valueAsDate = new Date();
            
            // Load master data
            await loadMasterData();
            
            // Initialize form
            initForm();
            
            // Add first product row
            addProductRow();
        });
        
        // Load master data
        async function loadMasterData() {
            try {
                [companies, categories] = await Promise.all([
                    getCompanies(),
                    getCategories()
                ]);
            } catch (error) {
                console.error('Error loading master data:', error);
                showToast('Error loading form data', 'error');
            }
        }
        
        // Initialize form functionality
        function initForm() {
            // Doctor search
            const doctorSearch = document.getElementById('doctorSearch');
            const debouncedDoctorSearch = debounce(async (query) => {
                if (query.length < 2) {
                    document.getElementById('doctorSuggestions').classList.remove('show');
                    return;
                }
                
                try {
                    const doctors = await suggestDoctorByName(query);
                    displayDoctorSuggestions(doctors);
                } catch (error) {
                    console.error('Error searching doctors:', error);
                }
            }, 300);
            
            doctorSearch.addEventListener('input', (e) => {
                debouncedDoctorSearch(e.target.value);
            });
            
            // Account search
            const accountSearch = document.getElementById('accountSearch');
            const debouncedAccountSearch = debounce(async (query) => {
                if (query.length < 2) {
                    document.getElementById('accountSuggestions').classList.remove('show');
                    return;
                }
                
                try {
                    const accounts = await suggestAccountByName(query);
                    displayAccountSuggestions(accounts);
                } catch (error) {
                    console.error('Error searching accounts:', error);
                }
            }, 300);
            
            accountSearch.addEventListener('input', (e) => {
                debouncedAccountSearch(e.target.value);
            });
            
            // Clear buttons
            document.getElementById('clearDoctor').addEventListener('click', clearDoctor);
            document.getElementById('clearAccount').addEventListener('click', clearAccount);
            
            // Add product button
            document.getElementById('addProductBtn').addEventListener('click', addProductRow);
            
            // Form submission
            document.getElementById('addCaseForm').addEventListener('submit', handleSubmit);
        }
        
        // Display doctor suggestions
        function displayDoctorSuggestions(doctors) {
            const suggestionsEl = document.getElementById('doctorSuggestions');
            
            if (doctors.length === 0) {
                suggestionsEl.classList.remove('show');
                return;
            }
            
            suggestionsEl.innerHTML = doctors.map(doctor => `
                <button type="button" class="dropdown-item" data-doctor-id="${doctor.id}">
                    <div>
                        <strong>${doctor.name}</strong>
                        ${doctor.specialty ? `<br><small class="text-muted">${doctor.specialty}</small>` : ''}
                    </div>
                </button>
            `).join('');
            
            // Add click handlers
            suggestionsEl.querySelectorAll('.dropdown-item').forEach(item => {
                item.addEventListener('click', () => {
                    const doctorId = item.dataset.doctorId;
                    const doctorName = item.querySelector('strong').textContent;
                    
                    document.getElementById('selectedDoctorId').value = doctorId;
                    document.getElementById('doctorSearch').value = '';
                    document.getElementById('selectedDoctorName').textContent = doctorName;
                    document.getElementById('selectedDoctor').classList.remove('d-none');
                    suggestionsEl.classList.remove('show');
                });
            });
            
            suggestionsEl.classList.add('show');
        }
        
        // Display account suggestions
        function displayAccountSuggestions(accounts) {
            const suggestionsEl = document.getElementById('accountSuggestions');
            
            if (accounts.length === 0) {
                suggestionsEl.classList.remove('show');
                return;
            }
            
            suggestionsEl.innerHTML = accounts.map(account => `
                <button type="button" class="dropdown-item" data-account-id="${account.id}">
                    <div>
                        <strong>${account.name}</strong>
                        <br><small class="text-muted">${account.type.toUpperCase()} - ${account.governorate}</small>
                    </div>
                </button>
            `).join('');
            
            // Add click handlers
            suggestionsEl.querySelectorAll('.dropdown-item').forEach(item => {
                item.addEventListener('click', () => {
                    const accountId = item.dataset.accountId;
                    const accountName = item.querySelector('strong').textContent;
                    
                    document.getElementById('selectedAccountId').value = accountId;
                    document.getElementById('accountSearch').value = '';
                    document.getElementById('selectedAccountName').textContent = accountName;
                    document.getElementById('selectedAccount').classList.remove('d-none');
                    suggestionsEl.classList.remove('show');
                });
            });
            
            suggestionsEl.classList.add('show');
        }
        
        // Clear doctor selection
        function clearDoctor() {
            document.getElementById('selectedDoctorId').value = '';
            document.getElementById('doctorSearch').value = '';
            document.getElementById('selectedDoctor').classList.add('d-none');
        }
        
        // Clear account selection
        function clearAccount() {
            document.getElementById('selectedAccountId').value = '';
            document.getElementById('accountSearch').value = '';
            document.getElementById('selectedAccount').classList.add('d-none');
        }
        
        // Add product row
        function addProductRow() {
            if (productCount >= 3) {
                showToast('Maximum 3 products allowed per case', 'warning');
                return;
            }
            
            productCount++;
            const template = document.getElementById('productRowTemplate');
            const clone = template.content.cloneNode(true);
            
            // Update product number
            clone.querySelector('.product-number').textContent = productCount;
            
            // Populate company dropdown
            const companySelect = clone.querySelector('.company-select');
            companies.forEach(company => {
                const option = document.createElement('option');
                option.value = company.id;
                option.textContent = company.name;
                companySelect.appendChild(option);
            });
            
            // Populate category dropdown
            const categorySelect = clone.querySelector('.category-select');
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
            
            // Setup cascading dropdowns
            setupProductRowEvents(clone);
            
            document.getElementById('productsContainer').appendChild(clone);
            
            // Update add button state
            updateAddProductButton();
        }
        
        // Setup events for product row
        function setupProductRowEvents(row) {
            const companySelect = row.querySelector('.company-select');
            const categorySelect = row.querySelector('.category-select');
            const productSelect = row.querySelector('.product-select');
            const removeBtn = row.querySelector('.remove-product-btn');
            
            // Company change - load products
            companySelect.addEventListener('change', async () => {
                await loadProductsForRow(row);
            });
            
            // Category change - filter products
            categorySelect.addEventListener('change', async () => {
                await loadProductsForRow(row);
            });
            
            // Remove button
            removeBtn.addEventListener('click', () => {
                row.closest('.product-row').remove();
                productCount--;
                updateProductNumbers();
                updateAddProductButton();
            });
        }
        
        // Load products for a specific row
        async function loadProductsForRow(row) {
            const companySelect = row.querySelector('.company-select');
            const categorySelect = row.querySelector('.category-select');
            const productSelect = row.querySelector('.product-select');
            
            const companyId = companySelect.value;
            const categoryId = categorySelect.value;
            
            if (!companyId) {
                productSelect.innerHTML = '<option value="">Select Product</option>';
                return;
            }
            
            try {
                const filters = { company_id: companyId };
                if (categoryId) {
                    filters.category_id = categoryId;
                }
                
                const products = await getApprovedProducts(filters);
                
                productSelect.innerHTML = '<option value="">Select Product</option>';
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = product.name;
                    productSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading products:', error);
                showToast('Error loading products', 'error');
            }
        }
        
        // Update product numbers
        function updateProductNumbers() {
            const rows = document.querySelectorAll('.product-row');
            rows.forEach((row, index) => {
                row.querySelector('.product-number').textContent = index + 1;
            });
        }
        
        // Update add product button state
        function updateAddProductButton() {
            const addBtn = document.getElementById('addProductBtn');
            addBtn.disabled = productCount >= 3;
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate basic form
            if (!validateForm(form)) {
                return;
            }
            
            // Validate doctor and account selection
            if (!document.getElementById('selectedDoctorId').value) {
                showToast('Please select a doctor', 'error');
                return;
            }
            
            if (!document.getElementById('selectedAccountId').value) {
                showToast('Please select an account', 'error');
                return;
            }
            
            // Validate products
            const productRows = document.querySelectorAll('.product-row');
            if (productRows.length === 0) {
                showToast('Please add at least one product', 'error');
                return;
            }
            
            const products = [];
            let validProducts = true;
            
            productRows.forEach((row, index) => {
                const companyId = row.querySelector('.company-select').value;
                const productId = row.querySelector('.product-select').value;
                const units = parseInt(row.querySelector('.units-input').value);
                
                if (!companyId || !productId || !units || units < 1) {
                    showToast(`Please complete product ${index + 1} information`, 'error');
                    validProducts = false;
                    return;
                }
                
                products.push({
                    company_id: parseInt(companyId),
                    product_id: parseInt(productId),
                    units: units
                });
            });
            
            if (!validProducts) return;
            
            try {
                showLoading(submitBtn);
                
                const caseData = {
                    case_date: document.getElementById('caseDate').value,
                    doctor_id: parseInt(document.getElementById('selectedDoctorId').value),
                    account_id: parseInt(document.getElementById('selectedAccountId').value),
                    comments: document.getElementById('caseComments').value.trim() || null,
                    products: products
                };
                
                await createCase(caseData);
                
                showToast('Case submitted successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/manager/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting case:', error);
                showToast(error.message || 'Error submitting case', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
        
        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.position-relative')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
