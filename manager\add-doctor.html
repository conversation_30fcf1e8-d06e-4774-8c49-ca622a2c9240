<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Doctor - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Add New Doctor</h1>
                            <p class="text-muted mb-0">Add a new doctor to the system</p>
                        </div>
                        <div>
                            <a href="/manager/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Doctor Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Doctor Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addDoctorForm">
                                <!-- Doctor Name -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="doctorName" class="form-label">Doctor Name *</label>
                                        <input type="text" class="form-control" id="doctorName" name="name" required 
                                               placeholder="Enter doctor's full name">
                                        <div class="invalid-feedback">
                                            Please provide the doctor's name.
                                        </div>
                                        <div id="duplicateWarning" class="alert alert-warning mt-2 d-none">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Possible duplicate found:</strong>
                                            <div id="duplicateList"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Specialty and Phone -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="doctorSpecialty" class="form-label">Specialty</label>
                                        <input type="text" class="form-control" id="doctorSpecialty" name="specialty" 
                                               placeholder="e.g., Cardiology, Orthopedics">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="doctorPhone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="doctorPhone" name="phone" 
                                               placeholder="e.g., +20 ************">
                                    </div>
                                </div>
                                
                                <!-- Account Associations -->
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <h6 class="mb-0">Account Associations</h6>
                                            <small class="text-muted">Associate this doctor with hospitals/clinics</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-primary" id="addAccountBtn">
                                            <i class="fas fa-plus me-1"></i>Add Account
                                        </button>
                                    </div>
                                    
                                    <div id="accountsContainer">
                                        <!-- Account associations will be added here -->
                                    </div>
                                    
                                    <div class="text-muted small mt-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        You can associate this doctor with multiple accounts. This is optional.
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Doctor
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/manager/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Account Association Template -->
    <template id="accountAssociationTemplate">
        <div class="account-association card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Account <span class="account-number"></span></h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-account-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="position-relative">
                    <input type="text" class="form-control account-search" 
                           placeholder="Search for account..." autocomplete="off">
                    <input type="hidden" class="selected-account-id">
                    <div class="account-suggestions dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                </div>
                
                <div class="selected-account mt-2 d-none">
                    <div class="alert alert-info py-2">
                        <strong>Selected:</strong> <span class="selected-account-name"></span>
                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2 clear-account-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </template>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            suggestDoctorByName, 
            suggestAccountByName,
            createDoctor 
        } from '/js/api.js';
        
        let accountCount = 0;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize form
            initForm();
        });
        
        // Initialize form functionality
        function initForm() {
            // Doctor name duplicate detection
            const doctorNameInput = document.getElementById('doctorName');
            const debouncedDuplicateCheck = debounce(async (name) => {
                if (name.length < 3) {
                    document.getElementById('duplicateWarning').classList.add('d-none');
                    return;
                }
                
                try {
                    const doctors = await suggestDoctorByName(name);
                    if (doctors.length > 0) {
                        displayDuplicateWarning(doctors);
                    } else {
                        document.getElementById('duplicateWarning').classList.add('d-none');
                    }
                } catch (error) {
                    console.error('Error checking for duplicates:', error);
                }
            }, 500);
            
            doctorNameInput.addEventListener('input', (e) => {
                debouncedDuplicateCheck(e.target.value);
            });
            
            // Add account button
            document.getElementById('addAccountBtn').addEventListener('click', addAccountAssociation);
            
            // Form submission
            document.getElementById('addDoctorForm').addEventListener('submit', handleSubmit);
        }
        
        // Display duplicate warning
        function displayDuplicateWarning(doctors) {
            const duplicateList = document.getElementById('duplicateList');
            duplicateList.innerHTML = doctors.map(doctor => `
                <div class="mt-1">
                    <strong>${doctor.name}</strong>
                    ${doctor.specialty ? ` - ${doctor.specialty}` : ''}
                    ${doctor.phone ? ` (${doctor.phone})` : ''}
                </div>
            `).join('');
            
            document.getElementById('duplicateWarning').classList.remove('d-none');
        }
        
        // Add account association
        function addAccountAssociation() {
            accountCount++;
            const template = document.getElementById('accountAssociationTemplate');
            const clone = template.content.cloneNode(true);
            
            // Update account number
            clone.querySelector('.account-number').textContent = accountCount;
            
            // Setup events for this association
            setupAccountAssociationEvents(clone);
            
            document.getElementById('accountsContainer').appendChild(clone);
        }
        
        // Setup events for account association
        function setupAccountAssociationEvents(association) {
            const searchInput = association.querySelector('.account-search');
            const suggestionsEl = association.querySelector('.account-suggestions');
            const selectedAccountId = association.querySelector('.selected-account-id');
            const selectedAccountEl = association.querySelector('.selected-account');
            const selectedAccountName = association.querySelector('.selected-account-name');
            const removeBtn = association.querySelector('.remove-account-btn');
            const clearBtn = association.querySelector('.clear-account-btn');
            
            // Account search
            const debouncedAccountSearch = debounce(async (query) => {
                if (query.length < 2) {
                    suggestionsEl.classList.remove('show');
                    return;
                }
                
                try {
                    const accounts = await suggestAccountByName(query);
                    displayAccountSuggestions(accounts, suggestionsEl, selectedAccountId, searchInput, selectedAccountEl, selectedAccountName);
                } catch (error) {
                    console.error('Error searching accounts:', error);
                }
            }, 300);
            
            searchInput.addEventListener('input', (e) => {
                debouncedAccountSearch(e.target.value);
            });
            
            // Remove association
            removeBtn.addEventListener('click', () => {
                association.closest('.account-association').remove();
                accountCount--;
                updateAccountNumbers();
            });
            
            // Clear selection
            clearBtn.addEventListener('click', () => {
                selectedAccountId.value = '';
                searchInput.value = '';
                selectedAccountEl.classList.add('d-none');
            });
        }
        
        // Display account suggestions
        function displayAccountSuggestions(accounts, suggestionsEl, selectedAccountId, searchInput, selectedAccountEl, selectedAccountName) {
            if (accounts.length === 0) {
                suggestionsEl.classList.remove('show');
                return;
            }
            
            suggestionsEl.innerHTML = accounts.map(account => `
                <button type="button" class="dropdown-item" data-account-id="${account.id}">
                    <div>
                        <strong>${account.name}</strong>
                        <br><small class="text-muted">${account.type.toUpperCase()} - ${account.governorate}</small>
                    </div>
                </button>
            `).join('');
            
            // Add click handlers
            suggestionsEl.querySelectorAll('.dropdown-item').forEach(item => {
                item.addEventListener('click', () => {
                    const accountId = item.dataset.accountId;
                    const accountName = item.querySelector('strong').textContent;
                    
                    selectedAccountId.value = accountId;
                    searchInput.value = '';
                    selectedAccountName.textContent = accountName;
                    selectedAccountEl.classList.remove('d-none');
                    suggestionsEl.classList.remove('show');
                });
            });
            
            suggestionsEl.classList.add('show');
        }
        
        // Update account numbers
        function updateAccountNumbers() {
            const associations = document.querySelectorAll('.account-association');
            associations.forEach((association, index) => {
                association.querySelector('.account-number').textContent = index + 1;
            });
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate basic form
            if (!validateForm(form)) {
                return;
            }
            
            // Get account associations
            const accountAssociations = [];
            const associations = document.querySelectorAll('.account-association');
            
            associations.forEach(association => {
                const accountId = association.querySelector('.selected-account-id').value;
                if (accountId) {
                    accountAssociations.push(parseInt(accountId));
                }
            });
            
            try {
                showLoading(submitBtn);
                
                const doctorData = {
                    name: document.getElementById('doctorName').value.trim(),
                    specialty: document.getElementById('doctorSpecialty').value.trim() || null,
                    phone: document.getElementById('doctorPhone').value.trim() || null,
                    account_associations: accountAssociations
                };
                
                await createDoctor(doctorData);
                
                showToast('Doctor submitted successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/manager/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting doctor:', error);
                showToast(error.message || 'Error submitting doctor', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
        
        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.position-relative')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
