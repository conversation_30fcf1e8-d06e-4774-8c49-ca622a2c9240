<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Product - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Suggest New Product</h1>
                            <p class="text-muted mb-0">Suggest a new product to be added to the system</p>
                        </div>
                        <div>
                            <a href="/manager/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Product Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Product Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addProductForm">
                                <!-- Product Name -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="productName" class="form-label">Product Name *</label>
                                        <input type="text" class="form-control" id="productName" name="name" required 
                                               placeholder="Enter product name">
                                        <div class="invalid-feedback">
                                            Please provide the product name.
                                        </div>
                                        <div id="duplicateWarning" class="alert alert-warning mt-2 d-none">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Possible duplicate found:</strong>
                                            <div id="duplicateList"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Company and Category -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="productCompany" class="form-label">Company *</label>
                                        <select class="form-select" id="productCompany" name="company_id" required>
                                            <option value="">Select Company</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a company.
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="productCategory" class="form-label">Category</label>
                                        <select class="form-select" id="productCategory" name="category_id">
                                            <option value="">Select Category (Optional)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Description -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <label for="productDescription" class="form-label">Description</label>
                                        <textarea class="form-control" id="productDescription" name="description" rows="3" 
                                                  placeholder="Optional description of the product, its uses, or specifications"></textarea>
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Product
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/manager/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            getCompanies,
            getCategories,
            getApprovedProducts,
            createProduct 
        } from '/js/api.js';
        
        let companies = [];
        let categories = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load master data
            await loadMasterData();
            
            // Initialize form
            initForm();
        });
        
        // Load master data
        async function loadMasterData() {
            try {
                [companies, categories] = await Promise.all([
                    getCompanies(),
                    getCategories()
                ]);
                
                // Populate company dropdown
                const companySelect = document.getElementById('productCompany');
                companies.forEach(company => {
                    const option = document.createElement('option');
                    option.value = company.id;
                    option.textContent = company.name;
                    companySelect.appendChild(option);
                });
                
                // Populate category dropdown
                const categorySelect = document.getElementById('productCategory');
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
                
            } catch (error) {
                console.error('Error loading master data:', error);
                showToast('Error loading form data', 'error');
            }
        }
        
        // Initialize form functionality
        function initForm() {
            // Product name duplicate detection
            const productNameInput = document.getElementById('productName');
            const productCompanySelect = document.getElementById('productCompany');
            
            const debouncedDuplicateCheck = debounce(async () => {
                const name = productNameInput.value.trim();
                const companyId = productCompanySelect.value;
                
                if (name.length < 3 || !companyId) {
                    document.getElementById('duplicateWarning').classList.add('d-none');
                    return;
                }
                
                try {
                    const products = await getApprovedProducts({ 
                        company_id: companyId,
                        search: name 
                    });
                    
                    // Filter for similar names
                    const similarProducts = products.filter(product => 
                        product.name.toLowerCase().includes(name.toLowerCase()) ||
                        name.toLowerCase().includes(product.name.toLowerCase())
                    );
                    
                    if (similarProducts.length > 0) {
                        displayDuplicateWarning(similarProducts);
                    } else {
                        document.getElementById('duplicateWarning').classList.add('d-none');
                    }
                } catch (error) {
                    console.error('Error checking for duplicates:', error);
                }
            }, 500);
            
            // Check for duplicates when name or company changes
            productNameInput.addEventListener('input', debouncedDuplicateCheck);
            productCompanySelect.addEventListener('change', debouncedDuplicateCheck);
            
            // Form submission
            document.getElementById('addProductForm').addEventListener('submit', handleSubmit);
        }
        
        // Display duplicate warning
        function displayDuplicateWarning(products) {
            const duplicateList = document.getElementById('duplicateList');
            duplicateList.innerHTML = products.map(product => {
                const company = companies.find(c => c.id === product.company_id);
                const category = categories.find(c => c.id === product.category_id);
                
                return `
                    <div class="mt-1">
                        <strong>${product.name}</strong> - ${company?.name || 'Unknown Company'}
                        ${category ? ` (${category.name})` : ''}
                    </div>
                `;
            }).join('');
            
            document.getElementById('duplicateWarning').classList.remove('d-none');
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            try {
                showLoading(submitBtn);
                
                const productData = {
                    name: document.getElementById('productName').value.trim(),
                    company_id: parseInt(document.getElementById('productCompany').value),
                    category_id: document.getElementById('productCategory').value ? 
                                parseInt(document.getElementById('productCategory').value) : null,
                    description: document.getElementById('productDescription').value.trim() || null
                };
                
                await createProduct(productData);
                
                showToast('Product submitted successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/manager/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting product:', error);
                showToast(error.message || 'Error submitting product', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
    </script>
</body>
</html>
