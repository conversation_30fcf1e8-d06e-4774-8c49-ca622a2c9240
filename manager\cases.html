<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Cases - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Team Cases</h1>
                            <p class="text-muted mb-0">View and manage your team's cases</p>
                        </div>
                        <div>
                            <a href="/manager/add-case.html" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add New Case
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div id="filtersContainer" class="mb-4">
                <!-- Filters will be rendered here -->
            </div>
            
            <!-- Cases Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Team Cases
                            </h5>
                            <div class="d-flex gap-2">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" id="refreshBtn">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" id="exportBtn">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="casesTable">
                                <!-- Smart table will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDate } from '/js/utils.js';
        import { getTeamCases } from '/js/api.js';
        import { SmartTable } from '/js/tables.js';
        import { initializeFilters } from '/js/filters.js';
        
        let casesTable;
        let filterManager;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize components
            await initializeComponents();
            
            // Load initial data
            await loadCases();
        });
        
        // Initialize page components
        async function initializeComponents() {
            // Initialize filters
            filterManager = initializeFilters('filtersContainer', {
                showDateRange: true,
                showCompany: true,
                showCategory: true,
                showProduct: true,
                showAccount: true,
                showDoctor: true,
                showGovernorate: true,
                showAccountType: true,
                showTextSearch: true,
                showStatus: true,
                showTeamMember: true // Manager-specific filter
            });
            
            // Initialize smart table
            casesTable = new SmartTable('casesTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: false, // Using separate filter component
                columnToggle: true
            });
            
            // Define table columns
            const columns = [
                {
                    key: 'case_date',
                    title: 'Date',
                    type: 'date',
                    width: '100px',
                    sortable: true
                },
                {
                    key: 'submitted_by',
                    title: 'Submitted By',
                    width: '150px',
                    sortable: true,
                    render: (value, row) => {
                        const user = row.users;
                        return user?.employees?.full_name || user?.username || 'N/A';
                    }
                },
                {
                    key: 'doctor_name',
                    title: 'Doctor',
                    width: '200px',
                    sortable: true,
                    render: (value, row) => {
                        const specialty = row.doctors?.specialty;
                        return `
                            <div>
                                <strong>${row.doctors?.name || 'N/A'}</strong>
                                ${specialty ? `<br><small class="text-muted">${specialty}</small>` : ''}
                            </div>
                        `;
                    }
                },
                {
                    key: 'account_name',
                    title: 'Account',
                    width: '200px',
                    sortable: true,
                    render: (value, row) => {
                        const account = row.accounts;
                        if (!account) return 'N/A';
                        return `
                            <div>
                                <strong>${account.name}</strong>
                                <br><small class="text-muted">${account.type.toUpperCase()} - ${account.governorate}</small>
                            </div>
                        `;
                    }
                },
                {
                    key: 'products',
                    title: 'Products',
                    width: '300px',
                    sortable: false,
                    render: (value, row) => {
                        const products = row.case_products || [];
                        if (products.length === 0) return 'No products';
                        
                        return products.map(cp => `
                            <div class="mb-1">
                                <strong>${cp.companies?.name || 'N/A'}</strong> - 
                                ${cp.products?.name || 'N/A'}
                                <span class="badge bg-primary ms-1">${cp.units} units</span>
                            </div>
                        `).join('');
                    }
                },
                {
                    key: 'status',
                    title: 'Status',
                    width: '100px',
                    sortable: true,
                    render: (value, row) => {
                        const statusMap = {
                            'pending': 'warning',
                            'approved': 'success',
                            'rejected': 'danger'
                        };
                        const badgeClass = statusMap[value] || 'secondary';
                        return `<span class="badge bg-${badgeClass}">${value.charAt(0).toUpperCase() + value.slice(1)}</span>`;
                    }
                },
                {
                    key: 'comments',
                    title: 'Comments',
                    width: '200px',
                    sortable: false,
                    render: (value, row) => {
                        if (!value) return '<span class="text-muted">No comments</span>';
                        return value.length > 50 ? 
                            `<span title="${value}">${value.substring(0, 50)}...</span>` : 
                            value;
                    }
                }
            ];
            
            casesTable.setColumns(columns);
            
            // Set up filter callback
            filterManager.onFiltersChange(async (filters) => {
                await loadCases(filters);
            });
            
            // Set up refresh button
            document.getElementById('refreshBtn').addEventListener('click', async () => {
                await loadCases(filterManager.getFilters());
                showToast('Cases refreshed', 'success');
            });
            
            // Set up export button
            document.getElementById('exportBtn').addEventListener('click', () => {
                casesTable.exportToExcel(casesTable.filteredData, 'team_cases_export');
            });
        }
        
        // Load cases data
        async function loadCases(filters = {}) {
            try {
                const cases = await getTeamCases(filters, 1000, 0); // Load more for better filtering
                casesTable.setData(cases);
                
                if (cases.length === 0) {
                    showToast('No cases found matching the current filters', 'info');
                }
            } catch (error) {
                console.error('Error loading cases:', error);
                showToast('Error loading cases', 'error');
                casesTable.setData([]);
            }
        }
    </script>
    
    <style>
        /* Custom styles for cases table */
        .table th {
            white-space: nowrap;
        }
        
        .table td {
            vertical-align: top;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        /* Responsive table adjustments */
        @media (max-width: 768px) {
            .table-responsive {
                font-size: 0.875rem;
            }
            
            .table td {
                padding: 0.5rem 0.25rem;
            }
        }
    </style>
</body>
</html>
