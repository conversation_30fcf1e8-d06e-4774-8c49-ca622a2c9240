<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manager Dashboard - Market Tracking System</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="welcome-header">
                        <h1 class="h3 mb-1 text-gradient">Manager Dashboard</h1>
                        <p class="text-muted mb-0" id="welcomeMessage">Welcome back, Manager</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-primary">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="teamMembers">-</h3>
                                    <p class="text-muted mb-0">Team Members</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-success">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="teamCases">-</h3>
                                    <p class="text-muted mb-0">Team Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="pendingApprovals">-</h3>
                                    <p class="text-muted mb-0">Pending Approvals</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="metric-icon bg-info">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0" id="monthlyTarget">-</h3>
                                    <p class="text-muted mb-0">Monthly Progress</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-xl-8 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Team Performance (Last 30 Days)</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" id="exportTeamChart">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="teamPerformanceChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-4 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Company Share</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" id="exportShareChart">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="companyShareChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions and Team Activity -->
            <div class="row">
                <div class="col-xl-4 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/manager/approvals.html" class="btn btn-warning">
                                    <i class="fas fa-tasks me-2"></i>Review Approvals
                                    <span class="badge bg-light text-dark ms-2" id="pendingBadge">0</span>
                                </a>
                                <a href="/manager/add-case.html" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>Add New Case
                                </a>
                                <a href="/manager/cases.html" class="btn btn-outline-info">
                                    <i class="fas fa-list me-2"></i>View Team Cases
                                </a>
                                <a href="/manager/tables.html" class="btn btn-outline-success">
                                    <i class="fas fa-table me-2"></i>Data Tables
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-8 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Team Activity
                            </h5>
                            <a href="/manager/cases.html" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <div id="teamActivity">
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, getSession, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDate, formatDateTime } from '/js/utils.js';
        import { createChart, exportChart } from '/js/charts.js';
        import { getTeamCases, getTeamMembers } from '/js/api.js';
        
        let currentUser;
        let teamPerformanceChart;
        let companyShareChart;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Get current user
            currentUser = getSession();
            
            // Load dashboard data
            await loadDashboardData();
            
            // Setup export buttons
            setupExportButtons();
        });
        
        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadMetrics(),
                    loadTeamPerformance(),
                    loadCompanyShare(),
                    loadTeamActivity()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showToast('Error loading dashboard data', 'error');
            }
        }
        
        // Load key metrics
        async function loadMetrics() {
            try {
                // Get team members
                const teamMembers = await getTeamMembers();
                document.getElementById('teamMembers').textContent = teamMembers.length;
                
                // Get team cases
                const teamCases = await getTeamCases();
                document.getElementById('teamCases').textContent = teamCases.length;
                
                // Count pending approvals
                const pendingCount = teamCases.filter(case_ => case_.status === 'pending').length;
                document.getElementById('pendingApprovals').textContent = pendingCount;
                document.getElementById('pendingBadge').textContent = pendingCount;
                
                // Calculate monthly progress (placeholder)
                const currentMonth = new Date().getMonth();
                const monthlyProgress = Math.round((new Date().getDate() / 30) * 100);
                document.getElementById('monthlyTarget').textContent = `${monthlyProgress}%`;
                
                // Update welcome message
                const employee = currentUser.employees;
                if (employee) {
                    document.getElementById('welcomeMessage').textContent = 
                        `Welcome back, ${employee.full_name} - ${employee.position_title}`;
                }
            } catch (error) {
                console.error('Error loading metrics:', error);
            }
        }
        
        // Load team performance chart
        async function loadTeamPerformance() {
            try {
                const teamCases = await getTeamCases();
                
                // Group cases by date for last 30 days
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                
                const casesData = {};
                const last30Days = [];
                
                for (let i = 29; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    const dateStr = date.toISOString().split('T')[0];
                    last30Days.push(dateStr);
                    casesData[dateStr] = { approved: 0, pending: 0, rejected: 0 };
                }
                
                teamCases.forEach(case_ => {
                    const caseDate = new Date(case_.case_date);
                    if (caseDate >= thirtyDaysAgo) {
                        const dateStr = case_.case_date;
                        if (casesData[dateStr]) {
                            casesData[dateStr][case_.status]++;
                        }
                    }
                });
                
                const chartData = {
                    labels: last30Days.map(date => new Date(date).toLocaleDateString()),
                    datasets: [
                        {
                            label: 'Approved',
                            data: last30Days.map(date => casesData[date].approved),
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Pending',
                            data: last30Days.map(date => casesData[date].pending),
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Rejected',
                            data: last30Days.map(date => casesData[date].rejected),
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            tension: 0.4
                        }
                    ]
                };
                
                teamPerformanceChart = createChart('teamPerformanceChart', 'line', chartData, {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading team performance:', error);
            }
        }
        
        // Load company share chart
        async function loadCompanyShare() {
            try {
                const teamCases = await getTeamCases({ status: 'approved' });
                
                // Count products by company
                const companyData = {};
                
                teamCases.forEach(case_ => {
                    if (case_.case_products) {
                        case_.case_products.forEach(cp => {
                            const companyName = cp.companies?.name || 'Unknown';
                            companyData[companyName] = (companyData[companyName] || 0) + cp.units;
                        });
                    }
                });
                
                const chartData = {
                    labels: Object.keys(companyData),
                    datasets: [{
                        data: Object.values(companyData),
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b',
                            '#38f9d7'
                        ]
                    }]
                };
                
                companyShareChart = createChart('companyShareChart', 'doughnut', chartData, {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading company share:', error);
            }
        }
        
        // Load team activity
        async function loadTeamActivity() {
            try {
                const teamCases = await getTeamCases({}, 10); // Get latest 10 cases
                
                const activityContainer = document.getElementById('teamActivity');
                
                if (!teamCases || teamCases.length === 0) {
                    activityContainer.innerHTML = `
                        <div class="text-center py-3 text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p class="mb-0">No recent team activity</p>
                        </div>
                    `;
                    return;
                }
                
                activityContainer.innerHTML = teamCases.map(case_ => {
                    const statusBadge = getStatusBadge(case_.status);
                    const userName = case_.users?.employees?.full_name || case_.users?.username || 'Unknown';
                    
                    return `
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="activity-icon me-3">
                                <i class="fas fa-clipboard-list text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">Case by ${userName}</h6>
                                        <p class="text-muted mb-1 small">
                                            Dr. ${case_.doctors?.name || 'N/A'} at ${case_.accounts?.name || 'N/A'}
                                        </p>
                                        <small class="text-muted">${formatDateTime(case_.created_at)}</small>
                                    </div>
                                    <div>
                                        ${statusBadge}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            } catch (error) {
                console.error('Error loading team activity:', error);
                document.getElementById('teamActivity').innerHTML = `
                    <div class="text-center py-3 text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p class="mb-0">Error loading team activity</p>
                    </div>
                `;
            }
        }
        
        // Get status badge HTML
        function getStatusBadge(status) {
            const statusMap = {
                'pending': 'warning',
                'approved': 'success',
                'rejected': 'danger'
            };
            const badgeClass = statusMap[status] || 'secondary';
            return `<span class="badge bg-${badgeClass}">${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;
        }
        
        // Setup export buttons
        function setupExportButtons() {
            document.getElementById('exportTeamChart').addEventListener('click', () => {
                if (teamPerformanceChart) {
                    exportChart(teamPerformanceChart, 'team_performance_chart');
                }
            });
            
            document.getElementById('exportShareChart').addEventListener('click', () => {
                if (companyShareChart) {
                    exportChart(companyShareChart, 'company_share_chart');
                }
            });
        }
    </script>
    
    <style>
        .activity-item {
            border-bottom: 1px solid #eee;
            padding-bottom: 1rem;
        }
        
        .activity-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .activity-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
</body>
</html>
