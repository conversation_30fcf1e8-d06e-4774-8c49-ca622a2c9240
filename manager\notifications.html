<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Notifications</h1>
                            <p class="text-muted mb-0">Stay updated with team activities and system notifications</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" id="markAllReadBtn">
                                <i class="fas fa-check-double me-2"></i>Mark All Read
                            </button>
                            <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
                                <i class="fas fa-sync-alt me-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notification Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body py-3">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="btn-group" role="group" id="statusFilter">
                                        <input type="radio" class="btn-check" name="status" id="all" value="all" checked>
                                        <label class="btn btn-outline-primary" for="all">All</label>
                                        
                                        <input type="radio" class="btn-check" name="status" id="unread" value="unread">
                                        <label class="btn btn-outline-primary" for="unread">Unread</label>
                                        
                                        <input type="radio" class="btn-check" name="status" id="read" value="read">
                                        <label class="btn btn-outline-primary" for="read">Read</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="btn-group" role="group" id="typeFilter">
                                        <input type="radio" class="btn-check" name="type" id="allTypes" value="all" checked>
                                        <label class="btn btn-outline-secondary" for="allTypes">All Types</label>
                                        
                                        <input type="radio" class="btn-check" name="type" id="approval" value="approval">
                                        <label class="btn btn-outline-secondary" for="approval">Approvals</label>
                                        
                                        <input type="radio" class="btn-check" name="type" id="rejection" value="rejection">
                                        <label class="btn btn-outline-secondary" for="rejection">Rejections</label>
                                        
                                        <input type="radio" class="btn-check" name="type" id="system" value="system">
                                        <label class="btn btn-outline-secondary" for="system">System</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notifications List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>Recent Notifications
                            </h5>
                            <span class="badge bg-primary" id="notificationCount">0</span>
                        </div>
                        <div class="card-body p-0">
                            <div id="notificationsList">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, getSession, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDateTime } from '/js/utils.js';
        
        let currentUser;
        let allNotifications = [];
        let filteredNotifications = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Get current user
            currentUser = getSession();
            
            // Load notifications
            await loadNotifications();
            
            // Setup event listeners
            setupEventListeners();
        });
        
        // Setup event listeners
        function setupEventListeners() {
            // Filter change handlers
            document.querySelectorAll('input[name="status"]').forEach(radio => {
                radio.addEventListener('change', filterNotifications);
            });
            
            document.querySelectorAll('input[name="type"]').forEach(radio => {
                radio.addEventListener('change', filterNotifications);
            });
            
            // Action buttons
            document.getElementById('markAllReadBtn').addEventListener('click', markAllAsRead);
            document.getElementById('refreshBtn').addEventListener('click', async () => {
                await loadNotifications();
                showToast('Notifications refreshed', 'success');
            });
        }
        
        // Load notifications (simulated based on team submissions)
        async function loadNotifications() {
            try {
                // Get team member IDs
                const { data: teamMembers, error: teamError } = await supabase
                    .from('employees')
                    .select('users!employee_id(id, username, employees(full_name))')
                    .eq('direct_manager_id', currentUser.employee_id);
                
                if (teamError) throw teamError;
                
                const teamMemberIds = teamMembers.map(member => member.users?.id).filter(id => id);
                
                if (teamMemberIds.length === 0) {
                    allNotifications = [];
                    displayNotifications([]);
                    return;
                }
                
                // Get recent submissions from team members
                const [casesResult, doctorsResult, accountsResult, productsResult] = await Promise.all([
                    supabase
                        .from('cases')
                        .select(`
                            id, status, created_at, case_date,
                            users!created_by(username, employees(full_name)),
                            doctors(name)
                        `)
                        .in('created_by', teamMemberIds)
                        .order('created_at', { ascending: false })
                        .limit(20),
                    
                    supabase
                        .from('doctors')
                        .select(`
                            id, name, status, created_at,
                            users!created_by(username, employees(full_name))
                        `)
                        .in('created_by', teamMemberIds)
                        .order('created_at', { ascending: false })
                        .limit(20),
                    
                    supabase
                        .from('accounts')
                        .select(`
                            id, name, status, created_at,
                            users!created_by(username, employees(full_name))
                        `)
                        .in('created_by', teamMemberIds)
                        .order('created_at', { ascending: false })
                        .limit(20),
                    
                    supabase
                        .from('products')
                        .select(`
                            id, name, status, created_at,
                            users!created_by(username, employees(full_name))
                        `)
                        .in('created_by', teamMemberIds)
                        .order('created_at', { ascending: false })
                        .limit(20)
                ]);
                
                // Convert to notifications
                const notifications = [];
                
                // Process cases
                if (casesResult.data) {
                    casesResult.data.forEach(case_ => {
                        notifications.push({
                            id: `case_${case_.id}`,
                            type: case_.status === 'approved' ? 'approval' : case_.status === 'rejected' ? 'rejection' : 'system',
                            title: `Case ${case_.status === 'approved' ? 'Approved' : case_.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `Case for ${case_.doctors?.name || 'Unknown Doctor'} on ${case_.case_date} has been ${case_.status}`,
                            submittedBy: case_.users?.employees?.full_name || case_.users?.username || 'Unknown',
                            timestamp: case_.created_at,
                            isRead: Math.random() > 0.3 // Simulate read status
                        });
                    });
                }
                
                // Process doctors
                if (doctorsResult.data) {
                    doctorsResult.data.forEach(doctor => {
                        notifications.push({
                            id: `doctor_${doctor.id}`,
                            type: doctor.status === 'approved' ? 'approval' : doctor.status === 'rejected' ? 'rejection' : 'system',
                            title: `Doctor ${doctor.status === 'approved' ? 'Approved' : doctor.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `Dr. ${doctor.name} has been ${doctor.status}`,
                            submittedBy: doctor.users?.employees?.full_name || doctor.users?.username || 'Unknown',
                            timestamp: doctor.created_at,
                            isRead: Math.random() > 0.3
                        });
                    });
                }
                
                // Process accounts
                if (accountsResult.data) {
                    accountsResult.data.forEach(account => {
                        notifications.push({
                            id: `account_${account.id}`,
                            type: account.status === 'approved' ? 'approval' : account.status === 'rejected' ? 'rejection' : 'system',
                            title: `Account ${account.status === 'approved' ? 'Approved' : account.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `Account "${account.name}" has been ${account.status}`,
                            submittedBy: account.users?.employees?.full_name || account.users?.username || 'Unknown',
                            timestamp: account.created_at,
                            isRead: Math.random() > 0.3
                        });
                    });
                }
                
                // Process products
                if (productsResult.data) {
                    productsResult.data.forEach(product => {
                        notifications.push({
                            id: `product_${product.id}`,
                            type: product.status === 'approved' ? 'approval' : product.status === 'rejected' ? 'rejection' : 'system',
                            title: `Product ${product.status === 'approved' ? 'Approved' : product.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `Product "${product.name}" has been ${product.status}`,
                            submittedBy: product.users?.employees?.full_name || product.users?.username || 'Unknown',
                            timestamp: product.created_at,
                            isRead: Math.random() > 0.3
                        });
                    });
                }
                
                // Sort by timestamp
                allNotifications = notifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                
                // Apply current filters
                filterNotifications();
                
            } catch (error) {
                console.error('Error loading notifications:', error);
                showToast('Error loading notifications', 'error');
                allNotifications = [];
                displayNotifications([]);
            }
        }
        
        // Filter notifications
        function filterNotifications() {
            const statusFilter = document.querySelector('input[name="status"]:checked').value;
            const typeFilter = document.querySelector('input[name="type"]:checked').value;
            
            filteredNotifications = allNotifications.filter(notification => {
                // Status filter
                if (statusFilter === 'read' && !notification.isRead) return false;
                if (statusFilter === 'unread' && notification.isRead) return false;
                
                // Type filter
                if (typeFilter !== 'all' && notification.type !== typeFilter) return false;
                
                return true;
            });
            
            displayNotifications(filteredNotifications);
        }
        
        // Display notifications
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationsList');
            const countBadge = document.getElementById('notificationCount');
            
            countBadge.textContent = notifications.length;
            
            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-bell-slash fa-3x mb-3"></i>
                        <h5>No notifications found</h5>
                        <p class="mb-0">No notifications match your current filters</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = notifications.map(notification => `
                <div class="notification-item border-bottom p-3 ${!notification.isRead ? 'bg-light' : ''}" 
                     data-notification-id="${notification.id}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <div class="notification-icon me-3">
                                    ${getNotificationIcon(notification.type)}
                                </div>
                                <div>
                                    <h6 class="mb-0 ${!notification.isRead ? 'fw-bold' : ''}">${notification.title}</h6>
                                    <small class="text-muted">by ${notification.submittedBy}</small>
                                </div>
                            </div>
                            <p class="mb-2 ${!notification.isRead ? 'fw-semibold' : 'text-muted'}">${notification.message}</p>
                            <small class="text-muted">${formatDateTime(notification.timestamp)}</small>
                        </div>
                        <div class="d-flex gap-2">
                            ${!notification.isRead ? `
                                <button class="btn btn-sm btn-outline-primary" onclick="markAsRead('${notification.id}')">
                                    <i class="fas fa-check"></i>
                                </button>
                            ` : `
                                <button class="btn btn-sm btn-outline-secondary" onclick="markAsUnread('${notification.id}')">
                                    <i class="fas fa-undo"></i>
                                </button>
                            `}
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Get notification icon
        function getNotificationIcon(type) {
            const iconMap = {
                'approval': '<i class="fas fa-check-circle text-success"></i>',
                'rejection': '<i class="fas fa-times-circle text-danger"></i>',
                'system': '<i class="fas fa-info-circle text-primary"></i>'
            };
            return iconMap[type] || iconMap.system;
        }
        
        // Mark notification as read
        window.markAsRead = function(notificationId) {
            const notification = allNotifications.find(n => n.id === notificationId);
            if (notification) {
                notification.isRead = true;
                filterNotifications();
                showToast('Marked as read', 'success');
            }
        };
        
        // Mark notification as unread
        window.markAsUnread = function(notificationId) {
            const notification = allNotifications.find(n => n.id === notificationId);
            if (notification) {
                notification.isRead = false;
                filterNotifications();
                showToast('Marked as unread', 'success');
            }
        };
        
        // Mark all as read
        function markAllAsRead() {
            allNotifications.forEach(notification => {
                notification.isRead = true;
            });
            filterNotifications();
            showToast('All notifications marked as read', 'success');
        }
    </script>
    
    <style>
        .notification-item:hover {
            background-color: rgba(0, 0, 0, 0.02) !important;
        }
        
        .notification-item:last-child {
            border-bottom: none !important;
        }
        
        .notification-icon {
            font-size: 1.25rem;
        }
    </style>
</body>
</html>
