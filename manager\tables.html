<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Tables - Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Data Tables</h1>
                            <p class="text-muted mb-0">Browse and manage system data</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
                                <i class="fas fa-sync-alt me-2"></i>Refresh Current
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Data Tables -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="tablesTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="doctors-tab" data-bs-toggle="tab" 
                                            data-bs-target="#doctors-pane" type="button" role="tab">
                                        <i class="fas fa-user-md me-2"></i>Doctors
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="accounts-tab" data-bs-toggle="tab" 
                                            data-bs-target="#accounts-pane" type="button" role="tab">
                                        <i class="fas fa-building me-2"></i>Accounts
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="products-tab" data-bs-toggle="tab" 
                                            data-bs-target="#products-pane" type="button" role="tab">
                                        <i class="fas fa-box me-2"></i>Products
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="companies-tab" data-bs-toggle="tab" 
                                            data-bs-target="#companies-pane" type="button" role="tab">
                                        <i class="fas fa-industry me-2"></i>Companies
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="tab-content" id="tablesTabContent">
                            <!-- Doctors Tab -->
                            <div class="tab-pane fade show active" id="doctors-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Doctors</h6>
                                        <div class="d-flex gap-2">
                                            <a href="/manager/add-doctor.html" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus me-1"></i>Add Doctor
                                            </a>
                                            <button class="btn btn-outline-primary btn-sm" id="exportDoctorsBtn">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="doctorsTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Accounts Tab -->
                            <div class="tab-pane fade" id="accounts-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Accounts</h6>
                                        <div class="d-flex gap-2">
                                            <a href="/manager/add-account.html" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus me-1"></i>Add Account
                                            </a>
                                            <button class="btn btn-outline-primary btn-sm" id="exportAccountsBtn">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="accountsTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Products Tab -->
                            <div class="tab-pane fade" id="products-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Products</h6>
                                        <div class="d-flex gap-2">
                                            <a href="/manager/add-product.html" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus me-1"></i>Suggest Product
                                            </a>
                                            <button class="btn btn-outline-primary btn-sm" id="exportProductsBtn">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="productsTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Companies Tab -->
                            <div class="tab-pane fade" id="companies-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Companies</h6>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm" id="exportCompaniesBtn">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="companiesTable">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDateTime } from '/js/utils.js';
        import { 
            getApprovedDoctors,
            getApprovedAccounts,
            getApprovedProducts,
            getCompanies 
        } from '/js/api.js';
        import { SmartTable } from '/js/tables.js';
        
        let doctorsTable, accountsTable, productsTable, companiesTable;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['district_manager', 'senior_manager'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize tables
            initializeTables();
            
            // Load initial data
            await loadDoctors();
            
            // Setup event listeners
            setupEventListeners();
        });
        
        // Initialize all tables
        function initializeTables() {
            // Doctors table
            doctorsTable = new SmartTable('doctorsTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            doctorsTable.setColumns([
                { key: 'name', title: 'Name', sortable: true, width: '200px' },
                { key: 'specialty', title: 'Specialty', sortable: true, width: '150px' },
                { key: 'phone', title: 'Phone', sortable: true, width: '150px' },
                { 
                    key: 'status', 
                    title: 'Status', 
                    sortable: true, 
                    width: '100px',
                    render: (value) => {
                        const statusMap = { 'pending': 'warning', 'approved': 'success', 'rejected': 'danger' };
                        const badgeClass = statusMap[value] || 'secondary';
                        return `<span class="badge bg-${badgeClass}">${value.charAt(0).toUpperCase() + value.slice(1)}</span>`;
                    }
                },
                { 
                    key: 'created_at', 
                    title: 'Created', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => formatDateTime(value)
                }
            ]);
            
            // Accounts table
            accountsTable = new SmartTable('accountsTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            accountsTable.setColumns([
                { key: 'name', title: 'Name', sortable: true, width: '250px' },
                { 
                    key: 'type', 
                    title: 'Type', 
                    sortable: true, 
                    width: '100px',
                    render: (value) => value.toUpperCase()
                },
                { key: 'governorate', title: 'Governorate', sortable: true, width: '150px' },
                { 
                    key: 'status', 
                    title: 'Status', 
                    sortable: true, 
                    width: '100px',
                    render: (value) => {
                        const statusMap = { 'pending': 'warning', 'approved': 'success', 'rejected': 'danger' };
                        const badgeClass = statusMap[value] || 'secondary';
                        return `<span class="badge bg-${badgeClass}">${value.charAt(0).toUpperCase() + value.slice(1)}</span>`;
                    }
                },
                { 
                    key: 'created_at', 
                    title: 'Created', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => formatDateTime(value)
                }
            ]);
            
            // Products table
            productsTable = new SmartTable('productsTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            productsTable.setColumns([
                { key: 'name', title: 'Product Name', sortable: true, width: '250px' },
                { 
                    key: 'company_name', 
                    title: 'Company', 
                    sortable: true, 
                    width: '200px',
                    render: (value, row) => row.companies?.name || 'N/A'
                },
                { 
                    key: 'category_name', 
                    title: 'Category', 
                    sortable: true, 
                    width: '150px',
                    render: (value, row) => row.categories?.name || 'N/A'
                },
                { 
                    key: 'status', 
                    title: 'Status', 
                    sortable: true, 
                    width: '100px',
                    render: (value) => {
                        const statusMap = { 'pending': 'warning', 'approved': 'success', 'rejected': 'danger' };
                        const badgeClass = statusMap[value] || 'secondary';
                        return `<span class="badge bg-${badgeClass}">${value.charAt(0).toUpperCase() + value.slice(1)}</span>`;
                    }
                },
                { 
                    key: 'created_at', 
                    title: 'Created', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => formatDateTime(value)
                }
            ]);
            
            // Companies table
            companiesTable = new SmartTable('companiesTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            companiesTable.setColumns([
                { key: 'name', title: 'Company Name', sortable: true, width: '300px' },
                { 
                    key: 'description', 
                    title: 'Description', 
                    sortable: false, 
                    width: '300px',
                    render: (value) => value || '<span class="text-muted">No description</span>'
                },
                { 
                    key: 'created_at', 
                    title: 'Created', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => formatDateTime(value)
                }
            ]);
        }
        
        // Setup event listeners
        function setupEventListeners() {
            // Tab change handlers
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', async (e) => {
                    const targetId = e.target.getAttribute('data-bs-target');
                    
                    switch (targetId) {
                        case '#doctors-pane':
                            await loadDoctors();
                            break;
                        case '#accounts-pane':
                            await loadAccounts();
                            break;
                        case '#products-pane':
                            await loadProducts();
                            break;
                        case '#companies-pane':
                            await loadCompanies();
                            break;
                    }
                });
            });
            
            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', async () => {
                const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
                switch (activeTab) {
                    case '#doctors-pane':
                        await loadDoctors();
                        break;
                    case '#accounts-pane':
                        await loadAccounts();
                        break;
                    case '#products-pane':
                        await loadProducts();
                        break;
                    case '#companies-pane':
                        await loadCompanies();
                        break;
                }
                showToast('Data refreshed', 'success');
            });
            
            // Export buttons
            document.getElementById('exportDoctorsBtn').addEventListener('click', () => {
                doctorsTable.exportToExcel(doctorsTable.filteredData, 'doctors_export');
            });
            
            document.getElementById('exportAccountsBtn').addEventListener('click', () => {
                accountsTable.exportToExcel(accountsTable.filteredData, 'accounts_export');
            });
            
            document.getElementById('exportProductsBtn').addEventListener('click', () => {
                productsTable.exportToExcel(productsTable.filteredData, 'products_export');
            });
            
            document.getElementById('exportCompaniesBtn').addEventListener('click', () => {
                companiesTable.exportToExcel(companiesTable.filteredData, 'companies_export');
            });
        }
        
        // Load doctors data
        async function loadDoctors() {
            try {
                const doctors = await getApprovedDoctors();
                doctorsTable.setData(doctors);
            } catch (error) {
                console.error('Error loading doctors:', error);
                showToast('Error loading doctors', 'error');
                doctorsTable.setData([]);
            }
        }
        
        // Load accounts data
        async function loadAccounts() {
            try {
                const accounts = await getApprovedAccounts();
                accountsTable.setData(accounts);
            } catch (error) {
                console.error('Error loading accounts:', error);
                showToast('Error loading accounts', 'error');
                accountsTable.setData([]);
            }
        }
        
        // Load products data
        async function loadProducts() {
            try {
                const products = await getApprovedProducts();
                productsTable.setData(products);
            } catch (error) {
                console.error('Error loading products:', error);
                showToast('Error loading products', 'error');
                productsTable.setData([]);
            }
        }
        
        // Load companies data
        async function loadCompanies() {
            try {
                const companies = await getCompanies();
                companiesTable.setData(companies);
            } catch (error) {
                console.error('Error loading companies:', error);
                showToast('Error loading companies', 'error');
                companiesTable.setData([]);
            }
        }
    </script>
</body>
</html>
