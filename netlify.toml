[build]
  publish = "."

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin", "senior_manager", "district_manager", "product_specialist"]}

[[headers]]
  for = "/*.js"
  [headers.values]
    Content-Type = "application/javascript"
    
[[headers]]
  for = "/*.css"
  [headers.values]
    Content-Type = "text/css"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
