<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Account - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Add New Account</h1>
                            <p class="text-muted mb-0">Add a new hospital or clinic to the system</p>
                        </div>
                        <div>
                            <a href="/ps/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Account Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Account Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addAccountForm">
                                <!-- Account Name -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="accountName" class="form-label">Account Name *</label>
                                        <input type="text" class="form-control" id="accountName" name="name" required 
                                               placeholder="Enter hospital/clinic name (Arabic supported)">
                                        <div class="invalid-feedback">
                                            Please enter the account name.
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Arabic text is supported. The system will check for similar names.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Account Type and Governorate -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="accountType" class="form-label">Account Type *</label>
                                        <select class="form-select" id="accountType" name="type" required>
                                            <option value="">Select Account Type</option>
                                            <option value="private">Private</option>
                                            <option value="upa">UPA (University/Public)</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select an account type.
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="governorate" class="form-label">Governorate *</label>
                                        <select class="form-select" id="governorate" name="governorate" required>
                                            <option value="">Select Governorate</option>
                                            <option value="Cairo">Cairo</option>
                                            <option value="Alexandria">Alexandria</option>
                                            <option value="Giza">Giza</option>
                                            <option value="Qalyubia">Qalyubia</option>
                                            <option value="Port Said">Port Said</option>
                                            <option value="Suez">Suez</option>
                                            <option value="Luxor">Luxor</option>
                                            <option value="Aswan">Aswan</option>
                                            <option value="Asyut">Asyut</option>
                                            <option value="Beheira">Beheira</option>
                                            <option value="Beni Suef">Beni Suef</option>
                                            <option value="Dakahlia">Dakahlia</option>
                                            <option value="Damietta">Damietta</option>
                                            <option value="Fayyum">Fayyum</option>
                                            <option value="Gharbia">Gharbia</option>
                                            <option value="Ismailia">Ismailia</option>
                                            <option value="Kafr el-Sheikh">Kafr el-Sheikh</option>
                                            <option value="Matrouh">Matrouh</option>
                                            <option value="Minya">Minya</option>
                                            <option value="Monufia">Monufia</option>
                                            <option value="New Valley">New Valley</option>
                                            <option value="North Sinai">North Sinai</option>
                                            <option value="Qena">Qena</option>
                                            <option value="Red Sea">Red Sea</option>
                                            <option value="Sharqia">Sharqia</option>
                                            <option value="Sohag">Sohag</option>
                                            <option value="South Sinai">South Sinai</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a governorate.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Duplicate Warning -->
                                <div id="duplicateWarning" class="alert alert-warning d-none">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Similar Account Found:</strong>
                                    <div id="duplicateDetails"></div>
                                    <div class="mt-2">
                                        <small>Please verify this is not a duplicate before submitting.</small>
                                    </div>
                                </div>
                                
                                <!-- Account Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-info-circle me-2"></i>Account Uniqueness
                                                </h6>
                                                <p class="card-text mb-0">
                                                    Accounts are considered unique by the combination of:
                                                    <strong>Name + Type + Governorate</strong>
                                                </p>
                                                <small class="text-muted">
                                                    For example, "Cairo Hospital" can exist as both Private and UPA in the same governorate,
                                                    but not as two Private accounts in Cairo.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Account
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/ps/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            suggestAccountByName,
            createAccount 
        } from '/js/api.js';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize form
            initForm();
        });
        
        // Initialize form functionality
        function initForm() {
            // Account name duplicate check
            const accountNameInput = document.getElementById('accountName');
            const accountTypeSelect = document.getElementById('accountType');
            const governorateSelect = document.getElementById('governorate');
            
            const debouncedDuplicateCheck = debounce(async () => {
                const name = accountNameInput.value.trim();
                const type = accountTypeSelect.value;
                const governorate = governorateSelect.value;
                
                if (name.length >= 3 && type && governorate) {
                    await checkForDuplicates(name, type, governorate);
                } else {
                    hideDuplicateWarning();
                }
            }, 500);
            
            accountNameInput.addEventListener('input', debouncedDuplicateCheck);
            accountTypeSelect.addEventListener('change', debouncedDuplicateCheck);
            governorateSelect.addEventListener('change', debouncedDuplicateCheck);
            
            // Form submission
            document.getElementById('addAccountForm').addEventListener('submit', handleSubmit);
        }
        
        // Check for duplicate accounts
        async function checkForDuplicates(name, type, governorate) {
            try {
                const similarAccounts = await suggestAccountByName(name);
                
                // Filter for exact matches in same type and governorate
                const exactMatches = similarAccounts.filter(account => 
                    account.type === type && 
                    account.governorate === governorate &&
                    account.name.toLowerCase() === name.toLowerCase()
                );
                
                // Filter for similar names in same type and governorate
                const similarInSameLocation = similarAccounts.filter(account => 
                    account.type === type && 
                    account.governorate === governorate &&
                    account.name.toLowerCase() !== name.toLowerCase()
                );
                
                if (exactMatches.length > 0) {
                    showDuplicateWarning(exactMatches, 'exact');
                } else if (similarInSameLocation.length > 0) {
                    showDuplicateWarning(similarInSameLocation, 'similar');
                } else {
                    hideDuplicateWarning();
                }
            } catch (error) {
                console.error('Error checking for duplicates:', error);
            }
        }
        
        // Show duplicate warning
        function showDuplicateWarning(accounts, type) {
            const warning = document.getElementById('duplicateWarning');
            const details = document.getElementById('duplicateDetails');
            
            const warningText = type === 'exact' ? 
                'An account with this exact name, type, and governorate already exists:' :
                'Similar accounts found in the same type and governorate:';
            
            warning.querySelector('strong').textContent = type === 'exact' ? 
                'Duplicate Account Found:' : 'Similar Account Found:';
            
            details.innerHTML = `
                <div class="mb-2">${warningText}</div>
                ${accounts.map(account => `
                    <div class="mb-1">
                        <strong>${account.name}</strong> - ${account.type.toUpperCase()} - ${account.governorate}
                    </div>
                `).join('')}
            `;
            
            if (type === 'exact') {
                warning.className = 'alert alert-danger';
            } else {
                warning.className = 'alert alert-warning';
            }
            
            warning.classList.remove('d-none');
        }
        
        // Hide duplicate warning
        function hideDuplicateWarning() {
            document.getElementById('duplicateWarning').classList.add('d-none');
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            // Check if there's an exact duplicate warning
            const duplicateWarning = document.getElementById('duplicateWarning');
            if (!duplicateWarning.classList.contains('d-none') && 
                duplicateWarning.classList.contains('alert-danger')) {
                showToast('Cannot submit: An identical account already exists', 'error');
                return;
            }
            
            try {
                showLoading(submitBtn);
                
                const accountData = {
                    name: document.getElementById('accountName').value.trim(),
                    type: document.getElementById('accountType').value,
                    governorate: document.getElementById('governorate').value
                };
                
                await createAccount(accountData);
                
                showToast('Account submitted for approval successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/ps/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting account:', error);
                if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
                    showToast('An account with this name, type, and governorate already exists', 'error');
                } else {
                    showToast(error.message || 'Error submitting account', 'error');
                }
            } finally {
                hideLoading(submitBtn);
            }
        }
    </script>
</body>
</html>
