<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Case - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Add New Case</h1>
                            <p class="text-muted mb-0">Record a new surgical case with product usage</p>
                        </div>
                        <div>
                            <a href="/ps/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Case Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Case Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addCaseForm">
                                <!-- Case Date -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="caseDate" class="form-label">Case Date *</label>
                                        <input type="date" class="form-control" id="caseDate" name="case_date" required>
                                        <div class="invalid-feedback">
                                            Please select a case date.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Doctor Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="doctorSelect" class="form-label">Doctor *</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="doctorInput" placeholder="Search for doctor..." autocomplete="off">
                                            <input type="hidden" id="doctorId" name="doctor_id" required>
                                            <div class="invalid-feedback">
                                                Please select a doctor.
                                            </div>
                                            <div id="doctorSuggestions" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Selected Doctor</label>
                                        <div id="selectedDoctor" class="form-control bg-light" style="min-height: 38px; display: flex; align-items: center;">
                                            <span class="text-muted">No doctor selected</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Account Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="accountInput" class="form-label">Account *</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="accountInput" placeholder="Search for account..." autocomplete="off">
                                            <input type="hidden" id="accountId" name="account_id" required>
                                            <div class="invalid-feedback">
                                                Please select an account.
                                            </div>
                                            <div id="accountSuggestions" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Selected Account</label>
                                        <div id="selectedAccount" class="form-control bg-light" style="min-height: 38px; display: flex; align-items: center;">
                                            <span class="text-muted">No account selected</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Products Section -->
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Products Used (Max 3)</h6>
                                        <button type="button" class="btn btn-sm btn-primary" id="addProductBtn">
                                            <i class="fas fa-plus me-1"></i>Add Product
                                        </button>
                                    </div>
                                    
                                    <div id="productsContainer">
                                        <!-- Product rows will be added here -->
                                    </div>
                                    
                                    <div class="text-muted small mt-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        You can add up to 3 products per case. Products can be from different companies.
                                    </div>
                                </div>
                                
                                <!-- Comments -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <label for="comments" class="form-label">Comments</label>
                                        <textarea class="form-control" id="comments" name="comments" rows="3" placeholder="Additional notes about this case..."></textarea>
                                    </div>
                                </div>
                                
                                <!-- Duplicate Warning -->
                                <div id="duplicateWarning" class="alert alert-warning d-none">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Possible Duplicate:</strong> A similar case may already exist. Please review before submitting.
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Case
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/ps/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Product Row Template -->
    <template id="productRowTemplate">
        <div class="product-row card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="mb-0">Product <span class="product-number"></span></h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-product-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Company *</label>
                        <select class="form-select company-select" required>
                            <option value="">Select Company</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Category *</label>
                        <select class="form-select category-select" required disabled>
                            <option value="">Select Category</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Product *</label>
                        <select class="form-select product-select" required disabled>
                            <option value="">Select Product</option>
                        </select>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Units *</label>
                        <input type="number" class="form-control units-input" min="1" required>
                    </div>
                </div>
            </div>
        </div>
    </template>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            suggestDoctorByName, 
            suggestAccountByName, 
            getCompanies, 
            getCategories, 
            getApprovedProducts,
            createCase 
        } from '/js/api.js';
        
        let companies = [];
        let categories = [];
        let productCount = 0;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load master data
            await loadMasterData();
            
            // Initialize form
            initForm();
            
            // Set default date to today
            document.getElementById('caseDate').valueAsDate = new Date();
        });
        
        // Load master data
        async function loadMasterData() {
            try {
                [companies, categories] = await Promise.all([
                    getCompanies(),
                    getCategories()
                ]);
            } catch (error) {
                console.error('Error loading master data:', error);
                showToast('Error loading form data', 'error');
            }
        }
        
        // Initialize form functionality
        function initForm() {
            // Doctor search
            const doctorInput = document.getElementById('doctorInput');
            const doctorSuggestions = document.getElementById('doctorSuggestions');
            
            const debouncedDoctorSearch = debounce(async (query) => {
                if (query.length < 2) {
                    doctorSuggestions.classList.remove('show');
                    return;
                }
                
                try {
                    const doctors = await suggestDoctorByName(query);
                    displayDoctorSuggestions(doctors);
                } catch (error) {
                    console.error('Error searching doctors:', error);
                }
            }, 300);
            
            doctorInput.addEventListener('input', (e) => {
                debouncedDoctorSearch(e.target.value);
            });
            
            // Account search
            const accountInput = document.getElementById('accountInput');
            const accountSuggestions = document.getElementById('accountSuggestions');
            
            const debouncedAccountSearch = debounce(async (query) => {
                if (query.length < 2) {
                    accountSuggestions.classList.remove('show');
                    return;
                }
                
                try {
                    const accounts = await suggestAccountByName(query);
                    displayAccountSuggestions(accounts);
                } catch (error) {
                    console.error('Error searching accounts:', error);
                }
            }, 300);
            
            accountInput.addEventListener('input', (e) => {
                debouncedAccountSearch(e.target.value);
            });
            
            // Add product button
            document.getElementById('addProductBtn').addEventListener('click', addProductRow);
            
            // Form submission
            document.getElementById('addCaseForm').addEventListener('submit', handleSubmit);
            
            // Add first product row
            addProductRow();
        }
        
        // Display doctor suggestions
        function displayDoctorSuggestions(doctors) {
            const suggestions = document.getElementById('doctorSuggestions');
            
            if (doctors.length === 0) {
                suggestions.classList.remove('show');
                return;
            }
            
            suggestions.innerHTML = doctors.map(doctor => `
                <button type="button" class="dropdown-item" data-doctor-id="${doctor.id}">
                    <div>
                        <strong>${doctor.name}</strong>
                        ${doctor.specialty ? `<br><small class="text-muted">${doctor.specialty}</small>` : ''}
                    </div>
                </button>
            `).join('');
            
            // Add click handlers
            suggestions.querySelectorAll('.dropdown-item').forEach(item => {
                item.addEventListener('click', () => {
                    selectDoctor(item.dataset.doctorId, item.textContent.trim());
                });
            });
            
            suggestions.classList.add('show');
        }
        
        // Select doctor
        function selectDoctor(doctorId, doctorName) {
            document.getElementById('doctorId').value = doctorId;
            document.getElementById('doctorInput').value = '';
            document.getElementById('selectedDoctor').innerHTML = `<strong>${doctorName}</strong>`;
            document.getElementById('doctorSuggestions').classList.remove('show');
        }
        
        // Display account suggestions
        function displayAccountSuggestions(accounts) {
            const suggestions = document.getElementById('accountSuggestions');
            
            if (accounts.length === 0) {
                suggestions.classList.remove('show');
                return;
            }
            
            suggestions.innerHTML = accounts.map(account => `
                <button type="button" class="dropdown-item" data-account-id="${account.id}">
                    <div>
                        <strong>${account.name}</strong>
                        <br><small class="text-muted">${account.type.toUpperCase()} - ${account.governorate}</small>
                    </div>
                </button>
            `).join('');
            
            // Add click handlers
            suggestions.querySelectorAll('.dropdown-item').forEach(item => {
                item.addEventListener('click', () => {
                    selectAccount(item.dataset.accountId, item.textContent.trim());
                });
            });
            
            suggestions.classList.add('show');
        }
        
        // Select account
        function selectAccount(accountId, accountName) {
            document.getElementById('accountId').value = accountId;
            document.getElementById('accountInput').value = '';
            document.getElementById('selectedAccount').innerHTML = `<strong>${accountName}</strong>`;
            document.getElementById('accountSuggestions').classList.remove('show');
        }
        
        // Add product row
        function addProductRow() {
            if (productCount >= 3) {
                showToast('Maximum 3 products allowed per case', 'warning');
                return;
            }
            
            productCount++;
            const template = document.getElementById('productRowTemplate');
            const clone = template.content.cloneNode(true);
            
            // Update product number
            clone.querySelector('.product-number').textContent = productCount;
            
            // Populate company dropdown
            const companySelect = clone.querySelector('.company-select');
            companies.forEach(company => {
                const option = document.createElement('option');
                option.value = company.id;
                option.textContent = company.name;
                companySelect.appendChild(option);
            });
            
            // Add event listeners
            const categorySelect = clone.querySelector('.category-select');
            const productSelect = clone.querySelector('.product-select');
            
            companySelect.addEventListener('change', () => {
                populateCategories(categorySelect, productSelect);
            });
            
            categorySelect.addEventListener('change', () => {
                populateProducts(companySelect.value, categorySelect.value, productSelect);
            });
            
            // Remove button
            clone.querySelector('.remove-product-btn').addEventListener('click', (e) => {
                e.target.closest('.product-row').remove();
                productCount--;
                updateProductNumbers();
            });
            
            document.getElementById('productsContainer').appendChild(clone);
            
            // Update add button state
            updateAddButtonState();
        }
        
        // Populate categories
        function populateCategories(categorySelect, productSelect) {
            categorySelect.innerHTML = '<option value="">Select Category</option>';
            productSelect.innerHTML = '<option value="">Select Product</option>';
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
            
            categorySelect.disabled = false;
            productSelect.disabled = true;
        }
        
        // Populate products
        async function populateProducts(companyId, categoryId, productSelect) {
            try {
                productSelect.innerHTML = '<option value="">Loading...</option>';
                productSelect.disabled = true;
                
                const products = await getApprovedProducts({
                    company_id: companyId,
                    category_id: categoryId
                });
                
                productSelect.innerHTML = '<option value="">Select Product</option>';
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = product.name;
                    productSelect.appendChild(option);
                });
                
                productSelect.disabled = false;
            } catch (error) {
                console.error('Error loading products:', error);
                productSelect.innerHTML = '<option value="">Error loading products</option>';
            }
        }
        
        // Update product numbers
        function updateProductNumbers() {
            const productRows = document.querySelectorAll('.product-row');
            productRows.forEach((row, index) => {
                row.querySelector('.product-number').textContent = index + 1;
            });
        }
        
        // Update add button state
        function updateAddButtonState() {
            const addBtn = document.getElementById('addProductBtn');
            addBtn.disabled = productCount >= 3;
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            // Validate products
            const productRows = document.querySelectorAll('.product-row');
            if (productRows.length === 0) {
                showToast('Please add at least one product', 'error');
                return;
            }
            
            // Collect product data
            const products = [];
            let isValid = true;
            
            productRows.forEach(row => {
                const companyId = row.querySelector('.company-select').value;
                const categoryId = row.querySelector('.category-select').value;
                const productId = row.querySelector('.product-select').value;
                const units = row.querySelector('.units-input').value;
                
                if (!companyId || !categoryId || !productId || !units) {
                    isValid = false;
                    return;
                }
                
                products.push({
                    company_id: parseInt(companyId),
                    category_id: parseInt(categoryId),
                    product_id: parseInt(productId),
                    units: parseInt(units)
                });
            });
            
            if (!isValid) {
                showToast('Please complete all product information', 'error');
                return;
            }
            
            try {
                showLoading(submitBtn);
                
                const caseData = {
                    case_date: document.getElementById('caseDate').value,
                    doctor_id: parseInt(document.getElementById('doctorId').value),
                    account_id: parseInt(document.getElementById('accountId').value),
                    comments: document.getElementById('comments').value
                };
                
                await createCase(caseData, products);
                
                showToast('Case submitted successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/ps/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting case:', error);
                showToast(error.message || 'Error submitting case', 'error');
            } finally {
                hideLoading(submitBtn);
            }
        }
    </script>
</body>
</html>
