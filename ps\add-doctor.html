<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Doctor - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Add New Doctor</h1>
                            <p class="text-muted mb-0">Add a new doctor to the system</p>
                        </div>
                        <div>
                            <a href="/ps/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Doctor Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Doctor Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addDoctorForm">
                                <!-- Doctor Name -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="doctorName" class="form-label">Doctor Name *</label>
                                        <input type="text" class="form-control" id="doctorName" name="name" required 
                                               placeholder="Enter doctor's full name (Arabic supported)">
                                        <div class="invalid-feedback">
                                            Please enter the doctor's name.
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Arabic text is supported. The system will check for similar names.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Phone and Specialty -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="doctorPhone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="doctorPhone" name="phone" 
                                               placeholder="Enter phone number">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="doctorSpecialty" class="form-label">Specialty</label>
                                        <input type="text" class="form-control" id="doctorSpecialty" name="specialty" 
                                               placeholder="e.g., Orthopedic Surgery, Cardiology">
                                    </div>
                                </div>
                                
                                <!-- Account Associations -->
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Associated Accounts</h6>
                                        <button type="button" class="btn btn-sm btn-primary" id="addAccountBtn">
                                            <i class="fas fa-plus me-1"></i>Add Account
                                        </button>
                                    </div>
                                    
                                    <div id="accountsContainer">
                                        <!-- Account associations will be added here -->
                                    </div>
                                    
                                    <div class="text-muted small mt-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Associate this doctor with hospitals/clinics where they practice.
                                    </div>
                                </div>
                                
                                <!-- Duplicate Warning -->
                                <div id="duplicateWarning" class="alert alert-warning d-none">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Similar Doctor Found:</strong>
                                    <div id="duplicateDetails"></div>
                                    <div class="mt-2">
                                        <small>Please verify this is not a duplicate before submitting.</small>
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Submit Doctor
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/ps/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Account Association Template -->
    <template id="accountRowTemplate">
        <div class="account-row card mb-2">
            <div class="card-body py-2">
                <div class="d-flex align-items-center gap-3">
                    <div class="flex-grow-1">
                        <div class="position-relative">
                            <input type="text" class="form-control form-control-sm account-search" 
                                   placeholder="Search for account..." autocomplete="off">
                            <input type="hidden" class="account-id">
                            <div class="account-suggestions dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-account-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="selected-account mt-2 d-none">
                    <small class="text-muted">Selected: <span class="account-name"></span></small>
                </div>
            </div>
        </div>
    </template>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            suggestAccountByName, 
            suggestDoctorByName,
            createDoctor 
        } from '/js/api.js';
        
        let accountCount = 0;
        let selectedAccounts = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize form
            initForm();
        });
        
        // Initialize form functionality
        function initForm() {
            // Doctor name duplicate check
            const doctorNameInput = document.getElementById('doctorName');
            const debouncedDuplicateCheck = debounce(async (name) => {
                if (name.length >= 3) {
                    await checkForDuplicates(name);
                } else {
                    hideDuplicateWarning();
                }
            }, 500);
            
            doctorNameInput.addEventListener('input', (e) => {
                debouncedDuplicateCheck(e.target.value);
            });
            
            // Add account button
            document.getElementById('addAccountBtn').addEventListener('click', addAccountRow);
            
            // Form submission
            document.getElementById('addDoctorForm').addEventListener('submit', handleSubmit);
            
            // Add first account row
            addAccountRow();
        }
        
        // Check for duplicate doctors
        async function checkForDuplicates(name) {
            try {
                const similarDoctors = await suggestDoctorByName(name);
                
                if (similarDoctors.length > 0) {
                    showDuplicateWarning(similarDoctors);
                } else {
                    hideDuplicateWarning();
                }
            } catch (error) {
                console.error('Error checking for duplicates:', error);
            }
        }
        
        // Show duplicate warning
        function showDuplicateWarning(doctors) {
            const warning = document.getElementById('duplicateWarning');
            const details = document.getElementById('duplicateDetails');
            
            details.innerHTML = doctors.map(doctor => `
                <div class="mb-1">
                    <strong>${doctor.name}</strong>
                    ${doctor.specialty ? ` - ${doctor.specialty}` : ''}
                    ${doctor.phone ? ` (${doctor.phone})` : ''}
                </div>
            `).join('');
            
            warning.classList.remove('d-none');
        }
        
        // Hide duplicate warning
        function hideDuplicateWarning() {
            document.getElementById('duplicateWarning').classList.add('d-none');
        }
        
        // Add account association row
        function addAccountRow() {
            accountCount++;
            const template = document.getElementById('accountRowTemplate');
            const clone = template.content.cloneNode(true);
            
            const accountSearch = clone.querySelector('.account-search');
            const accountSuggestions = clone.querySelector('.account-suggestions');
            const accountId = clone.querySelector('.account-id');
            const selectedAccount = clone.querySelector('.selected-account');
            const accountName = clone.querySelector('.account-name');
            
            // Account search functionality
            const debouncedAccountSearch = debounce(async (query) => {
                if (query.length < 2) {
                    accountSuggestions.classList.remove('show');
                    return;
                }
                
                try {
                    const accounts = await suggestAccountByName(query);
                    displayAccountSuggestions(accounts, accountSuggestions, accountId, accountSearch, selectedAccount, accountName);
                } catch (error) {
                    console.error('Error searching accounts:', error);
                }
            }, 300);
            
            accountSearch.addEventListener('input', (e) => {
                debouncedAccountSearch(e.target.value);
            });
            
            // Remove button
            clone.querySelector('.remove-account-btn').addEventListener('click', (e) => {
                const row = e.target.closest('.account-row');
                const accountIdValue = row.querySelector('.account-id').value;
                if (accountIdValue) {
                    selectedAccounts = selectedAccounts.filter(id => id !== accountIdValue);
                }
                row.remove();
                accountCount--;
            });
            
            document.getElementById('accountsContainer').appendChild(clone);
        }
        
        // Display account suggestions
        function displayAccountSuggestions(accounts, suggestionsEl, accountIdEl, searchEl, selectedEl, nameEl) {
            if (accounts.length === 0) {
                suggestionsEl.classList.remove('show');
                return;
            }
            
            // Filter out already selected accounts
            const availableAccounts = accounts.filter(account => !selectedAccounts.includes(account.id.toString()));
            
            if (availableAccounts.length === 0) {
                suggestionsEl.innerHTML = '<div class="dropdown-item-text text-muted">All matching accounts already selected</div>';
                suggestionsEl.classList.add('show');
                return;
            }
            
            suggestionsEl.innerHTML = availableAccounts.map(account => `
                <button type="button" class="dropdown-item" data-account-id="${account.id}">
                    <div>
                        <strong>${account.name}</strong>
                        <br><small class="text-muted">${account.type.toUpperCase()} - ${account.governorate}</small>
                    </div>
                </button>
            `).join('');
            
            // Add click handlers
            suggestionsEl.querySelectorAll('.dropdown-item').forEach(item => {
                item.addEventListener('click', () => {
                    const accountId = item.dataset.accountId;
                    const accountText = item.textContent.trim();
                    
                    accountIdEl.value = accountId;
                    searchEl.value = '';
                    nameEl.textContent = accountText;
                    selectedEl.classList.remove('d-none');
                    suggestionsEl.classList.remove('show');
                    
                    selectedAccounts.push(accountId);
                });
            });
            
            suggestionsEl.classList.add('show');
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            // Collect account associations
            const accountRows = document.querySelectorAll('.account-row');
            const accountIds = [];
            
            accountRows.forEach(row => {
                const accountId = row.querySelector('.account-id').value;
                if (accountId) {
                    accountIds.push(parseInt(accountId));
                }
            });
            
            if (accountIds.length === 0) {
                showToast('Please associate the doctor with at least one account', 'error');
                return;
            }
            
            try {
                showLoading(submitBtn);
                
                const doctorData = {
                    name: document.getElementById('doctorName').value.trim(),
                    phone: document.getElementById('doctorPhone').value.trim() || null,
                    specialty: document.getElementById('doctorSpecialty').value.trim() || null
                };
                
                // Create doctor
                const doctor = await createDoctor(doctorData);
                
                // TODO: Create doctor-account associations
                // This would require additional API endpoints
                
                showToast('Doctor submitted for approval successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/ps/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting doctor:', error);
                if (error.message.includes('duplicate key')) {
                    showToast('A doctor with this name already exists', 'error');
                } else {
                    showToast(error.message || 'Error submitting doctor', 'error');
                }
            } finally {
                hideLoading(submitBtn);
            }
        }
        
        // Close suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.position-relative')) {
                document.querySelectorAll('.account-suggestions').forEach(suggestions => {
                    suggestions.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
