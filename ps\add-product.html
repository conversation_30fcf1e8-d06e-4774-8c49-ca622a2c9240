<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suggest Product - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Suggest New Product</h1>
                            <p class="text-muted mb-0">Suggest a new product to be added to the system</p>
                        </div>
                        <div>
                            <a href="/ps/dashboard.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Suggest Product Form -->
            <div class="row">
                <div class="col-xl-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Product Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="addProductForm">
                                <!-- Company Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="companySelect" class="form-label">Company *</label>
                                        <select class="form-select" id="companySelect" name="company_id" required>
                                            <option value="">Select Company</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a company.
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="categorySelect" class="form-label">Category *</label>
                                        <select class="form-select" id="categorySelect" name="category_id" required>
                                            <option value="">Select Category</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Product Name -->
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="productName" class="form-label">Product Name *</label>
                                        <input type="text" class="form-control" id="productName" name="name" required 
                                               placeholder="Enter product name">
                                        <div class="invalid-feedback">
                                            Please enter the product name.
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            The system will check for similar products within the same company.
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Duplicate Warning -->
                                <div id="duplicateWarning" class="alert alert-warning d-none">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Similar Product Found:</strong>
                                    <div id="duplicateDetails"></div>
                                    <div class="mt-2">
                                        <small>Please verify this is not a duplicate before submitting.</small>
                                    </div>
                                </div>
                                
                                <!-- Product Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-info-circle me-2"></i>Product Suggestion Process
                                                </h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="card-text mb-2">
                                                            <strong>Approval Process:</strong>
                                                        </p>
                                                        <ul class="small mb-0">
                                                            <li>Your suggestion will be reviewed by your manager</li>
                                                            <li>Final approval by system administrator</li>
                                                            <li>Once approved, the product will be available for case entry</li>
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="card-text mb-2">
                                                            <strong>Product Uniqueness:</strong>
                                                        </p>
                                                        <ul class="small mb-0">
                                                            <li>Products are unique within each company</li>
                                                            <li>Same product name can exist for different companies</li>
                                                            <li>Categories help organize products for easier selection</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Submit Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="fas fa-lightbulb me-2"></i>Submit Suggestion
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="location.href='/ps/dashboard.html'">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, validateForm, showLoading, hideLoading, debounce } from '/js/utils.js';
        import { 
            getCompanies,
            getCategories,
            getApprovedProducts,
            createProduct 
        } from '/js/api.js';
        
        let companies = [];
        let categories = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load master data
            await loadMasterData();
            
            // Initialize form
            initForm();
        });
        
        // Load master data
        async function loadMasterData() {
            try {
                [companies, categories] = await Promise.all([
                    getCompanies(),
                    getCategories()
                ]);
                
                populateCompanies();
                populateCategories();
            } catch (error) {
                console.error('Error loading master data:', error);
                showToast('Error loading form data', 'error');
            }
        }
        
        // Populate companies dropdown
        function populateCompanies() {
            const companySelect = document.getElementById('companySelect');
            companySelect.innerHTML = '<option value="">Select Company</option>';
            
            companies.forEach(company => {
                const option = document.createElement('option');
                option.value = company.id;
                option.textContent = company.name;
                companySelect.appendChild(option);
            });
        }
        
        // Populate categories dropdown
        function populateCategories() {
            const categorySelect = document.getElementById('categorySelect');
            categorySelect.innerHTML = '<option value="">Select Category</option>';
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        }
        
        // Initialize form functionality
        function initForm() {
            // Product name duplicate check
            const productNameInput = document.getElementById('productName');
            const companySelect = document.getElementById('companySelect');
            
            const debouncedDuplicateCheck = debounce(async () => {
                const name = productNameInput.value.trim();
                const companyId = companySelect.value;
                
                if (name.length >= 3 && companyId) {
                    await checkForDuplicates(name, companyId);
                } else {
                    hideDuplicateWarning();
                }
            }, 500);
            
            productNameInput.addEventListener('input', debouncedDuplicateCheck);
            companySelect.addEventListener('change', debouncedDuplicateCheck);
            
            // Form submission
            document.getElementById('addProductForm').addEventListener('submit', handleSubmit);
        }
        
        // Check for duplicate products
        async function checkForDuplicates(name, companyId) {
            try {
                const existingProducts = await getApprovedProducts({
                    company_id: companyId,
                    search: name
                });
                
                // Filter for exact matches within the same company
                const exactMatches = existingProducts.filter(product => 
                    product.name.toLowerCase() === name.toLowerCase()
                );
                
                // Filter for similar names within the same company
                const similarMatches = existingProducts.filter(product => 
                    product.name.toLowerCase() !== name.toLowerCase() &&
                    product.name.toLowerCase().includes(name.toLowerCase())
                );
                
                if (exactMatches.length > 0) {
                    showDuplicateWarning(exactMatches, 'exact');
                } else if (similarMatches.length > 0) {
                    showDuplicateWarning(similarMatches, 'similar');
                } else {
                    hideDuplicateWarning();
                }
            } catch (error) {
                console.error('Error checking for duplicates:', error);
            }
        }
        
        // Show duplicate warning
        function showDuplicateWarning(products, type) {
            const warning = document.getElementById('duplicateWarning');
            const details = document.getElementById('duplicateDetails');
            
            const warningText = type === 'exact' ? 
                'A product with this exact name already exists for this company:' :
                'Similar products found for this company:';
            
            warning.querySelector('strong').textContent = type === 'exact' ? 
                'Duplicate Product Found:' : 'Similar Product Found:';
            
            details.innerHTML = `
                <div class="mb-2">${warningText}</div>
                ${products.map(product => `
                    <div class="mb-1">
                        <strong>${product.name}</strong>
                        ${product.categories ? ` - ${product.categories.name}` : ''}
                    </div>
                `).join('')}
            `;
            
            if (type === 'exact') {
                warning.className = 'alert alert-danger';
            } else {
                warning.className = 'alert alert-warning';
            }
            
            warning.classList.remove('d-none');
        }
        
        // Hide duplicate warning
        function hideDuplicateWarning() {
            document.getElementById('duplicateWarning').classList.add('d-none');
        }
        
        // Handle form submission
        async function handleSubmit(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            
            // Validate form
            if (!validateForm(form)) {
                return;
            }
            
            // Check if there's an exact duplicate warning
            const duplicateWarning = document.getElementById('duplicateWarning');
            if (!duplicateWarning.classList.contains('d-none') && 
                duplicateWarning.classList.contains('alert-danger')) {
                showToast('Cannot submit: An identical product already exists for this company', 'error');
                return;
            }
            
            try {
                showLoading(submitBtn);
                
                const productData = {
                    company_id: parseInt(document.getElementById('companySelect').value),
                    category_id: parseInt(document.getElementById('categorySelect').value),
                    name: document.getElementById('productName').value.trim()
                };
                
                await createProduct(productData);
                
                showToast('Product suggestion submitted for approval successfully!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/ps/dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('Error submitting product:', error);
                if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
                    showToast('A product with this name already exists for this company', 'error');
                } else {
                    showToast(error.message || 'Error submitting product suggestion', 'error');
                }
            } finally {
                hideLoading(submitBtn);
            }
        }
    </script>
</body>
</html>
