<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Dashboard</h1>
                            <p class="text-muted mb-0">Welcome back, <span class="user-name"></span></p>
                        </div>
                        <div>
                            <button class="btn btn-primary" onclick="location.href='/ps/add-case.html'">
                                <i class="fas fa-plus me-2"></i>Add New Case
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card gradient-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="h4 mb-0" id="totalCases">0</div>
                                    <div class="small">Total Cases</div>
                                </div>
                                <div class="opacity-75">
                                    <i class="fas fa-clipboard-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card gradient-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="h4 mb-0" id="thisMonthCases">0</div>
                                    <div class="small">This Month</div>
                                </div>
                                <div class="opacity-75">
                                    <i class="fas fa-calendar-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card gradient-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="h4 mb-0" id="pendingApprovals">0</div>
                                    <div class="small">Pending Approvals</div>
                                </div>
                                <div class="opacity-75">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card gradient-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="h4 mb-0" id="activeDoctors">0</div>
                                    <div class="small">Active Doctors</div>
                                </div>
                                <div class="opacity-75">
                                    <i class="fas fa-user-md fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-xl-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Monthly Cases Trend</h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary active" data-period="6">6M</button>
                                <button class="btn btn-outline-primary" data-period="12">1Y</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="casesChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Company Share</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="companyChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row">
                <div class="col-xl-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recent Cases</h5>
                            <a href="/ps/cases.html" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Doctor</th>
                                            <th>Account</th>
                                            <th>Products</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recentCasesTable">
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/ps/add-case.html" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add New Case
                                </a>
                                <a href="/ps/add-doctor.html" class="btn btn-outline-primary">
                                    <i class="fas fa-user-md me-2"></i>Add Doctor
                                </a>
                                <a href="/ps/add-account.html" class="btn btn-outline-primary">
                                    <i class="fas fa-hospital me-2"></i>Add Account
                                </a>
                                <a href="/ps/add-product.html" class="btn btn-outline-primary">
                                    <i class="fas fa-box me-2"></i>Suggest Product
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Notifications</h5>
                        </div>
                        <div class="card-body">
                            <div id="notificationsList">
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDate, formatNumber } from '/js/utils.js';
        import { getApprovedCases } from '/js/api.js';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Load dashboard data
            await loadDashboardData();
            await loadRecentCases();
            await loadNotifications();
            
            // Initialize charts
            initCharts();
        });
        
        // Load dashboard statistics
        async function loadDashboardData() {
            try {
                // This would be replaced with actual API calls
                document.getElementById('totalCases').textContent = '0';
                document.getElementById('thisMonthCases').textContent = '0';
                document.getElementById('pendingApprovals').textContent = '0';
                document.getElementById('activeDoctors').textContent = '0';
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showToast('Error loading dashboard data', 'error');
            }
        }
        
        // Load recent cases
        async function loadRecentCases() {
            try {
                const cases = await getApprovedCases({}, 5, 0);
                const tbody = document.getElementById('recentCasesTable');
                
                if (cases.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="5" class="text-center py-4 text-muted">
                                No recent cases found
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                tbody.innerHTML = cases.map(case_ => `
                    <tr>
                        <td>${formatDate(case_.case_date)}</td>
                        <td>${case_.doctors?.name || 'N/A'}</td>
                        <td>${case_.accounts?.name || 'N/A'}</td>
                        <td>${case_.case_products?.length || 0} products</td>
                        <td>
                            <span class="badge bg-success">Approved</span>
                        </td>
                    </tr>
                `).join('');
            } catch (error) {
                console.error('Error loading recent cases:', error);
                document.getElementById('recentCasesTable').innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4 text-danger">
                            Error loading cases
                        </td>
                    </tr>
                `;
            }
        }
        
        // Load notifications
        async function loadNotifications() {
            try {
                const notificationsList = document.getElementById('notificationsList');
                notificationsList.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-bell-slash fa-2x mb-2 d-block"></i>
                        No new notifications
                    </div>
                `;
            } catch (error) {
                console.error('Error loading notifications:', error);
            }
        }
        
        // Initialize charts
        function initCharts() {
            // Cases trend chart
            const casesCtx = document.getElementById('casesChart').getContext('2d');
            new Chart(casesCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Cases',
                        data: [0, 0, 0, 0, 0, 0],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Company share chart
            const companyCtx = document.getElementById('companyChart').getContext('2d');
            new Chart(companyCtx, {
                type: 'doughnut',
                data: {
                    labels: ['No Data'],
                    datasets: [{
                        data: [1],
                        backgroundColor: ['#e9ecef'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
