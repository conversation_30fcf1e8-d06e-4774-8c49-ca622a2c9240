<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Notifications</h1>
                            <p class="text-muted mb-0">Stay updated with your submissions and approvals</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" id="markAllReadBtn">
                                <i class="fas fa-check-double me-2"></i>Mark All Read
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notification Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body py-2">
                            <div class="d-flex flex-wrap gap-2 align-items-center">
                                <span class="text-muted small">Filter:</span>
                                <div class="btn-group btn-group-sm" role="group">
                                    <input type="radio" class="btn-check" name="statusFilter" id="all" value="all" checked>
                                    <label class="btn btn-outline-primary" for="all">All</label>
                                    
                                    <input type="radio" class="btn-check" name="statusFilter" id="unread" value="unread">
                                    <label class="btn btn-outline-primary" for="unread">Unread</label>
                                    
                                    <input type="radio" class="btn-check" name="statusFilter" id="read" value="read">
                                    <label class="btn btn-outline-primary" for="read">Read</label>
                                </div>
                                
                                <div class="btn-group btn-group-sm ms-3" role="group">
                                    <input type="radio" class="btn-check" name="typeFilter" id="allTypes" value="all" checked>
                                    <label class="btn btn-outline-secondary" for="allTypes">All Types</label>
                                    
                                    <input type="radio" class="btn-check" name="typeFilter" id="approvals" value="approval">
                                    <label class="btn btn-outline-secondary" for="approvals">Approvals</label>
                                    
                                    <input type="radio" class="btn-check" name="typeFilter" id="rejections" value="rejection">
                                    <label class="btn btn-outline-secondary" for="rejections">Rejections</label>
                                    
                                    <input type="radio" class="btn-check" name="typeFilter" id="system" value="system">
                                    <label class="btn btn-outline-secondary" for="system">System</label>
                                </div>
                                
                                <button class="btn btn-outline-primary btn-sm ms-auto" id="refreshBtn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notifications List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>Your Notifications
                                <span class="badge bg-primary ms-2" id="unreadCount">0</span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div id="notificationsList">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth, getSession, supabase } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast, formatDateTime } from '/js/utils.js';
        
        let currentUser;
        let notifications = [];
        let filteredNotifications = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Get current user
            currentUser = getSession();
            
            // Load notifications
            await loadNotifications();
            
            // Setup event listeners
            setupEventListeners();
            
            // Auto-refresh every 30 seconds
            setInterval(loadNotifications, 30000);
        });
        
        // Setup event listeners
        function setupEventListeners() {
            // Filter buttons
            document.querySelectorAll('input[name="statusFilter"]').forEach(radio => {
                radio.addEventListener('change', filterNotifications);
            });
            
            document.querySelectorAll('input[name="typeFilter"]').forEach(radio => {
                radio.addEventListener('change', filterNotifications);
            });
            
            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', loadNotifications);
            
            // Mark all read button
            document.getElementById('markAllReadBtn').addEventListener('click', markAllAsRead);
        }
        
        // Load notifications from database
        async function loadNotifications() {
            try {
                // For now, we'll simulate notifications based on user's submissions
                // In a real system, you'd have a notifications table
                const [casesResult, doctorsResult, accountsResult, productsResult] = await Promise.all([
                    supabase.from('cases').select('*').eq('created_by', currentUser.id).order('created_at', { ascending: false }),
                    supabase.from('doctors').select('*').eq('created_by', currentUser.id).order('created_at', { ascending: false }),
                    supabase.from('accounts').select('*').eq('created_by', currentUser.id).order('created_at', { ascending: false }),
                    supabase.from('products').select('*').eq('created_by', currentUser.id).order('created_at', { ascending: false })
                ]);
                
                notifications = [];
                
                // Generate notifications from cases
                if (casesResult.data) {
                    casesResult.data.forEach(case_ => {
                        notifications.push({
                            id: `case_${case_.id}`,
                            type: case_.status === 'approved' ? 'approval' : case_.status === 'rejected' ? 'rejection' : 'system',
                            title: `Case ${case_.status === 'approved' ? 'Approved' : case_.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `Your case submission for ${case_.case_date} has been ${case_.status}`,
                            created_at: case_.updated_at || case_.created_at,
                            read: Math.random() > 0.3, // Simulate some read/unread
                            icon: case_.status === 'approved' ? 'check-circle' : case_.status === 'rejected' ? 'times-circle' : 'clock',
                            color: case_.status === 'approved' ? 'success' : case_.status === 'rejected' ? 'danger' : 'warning'
                        });
                    });
                }
                
                // Generate notifications from doctors
                if (doctorsResult.data) {
                    doctorsResult.data.forEach(doctor => {
                        notifications.push({
                            id: `doctor_${doctor.id}`,
                            type: doctor.status === 'approved' ? 'approval' : doctor.status === 'rejected' ? 'rejection' : 'system',
                            title: `Doctor ${doctor.status === 'approved' ? 'Approved' : doctor.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `Dr. ${doctor.name} has been ${doctor.status}`,
                            created_at: doctor.updated_at || doctor.created_at,
                            read: Math.random() > 0.3,
                            icon: doctor.status === 'approved' ? 'check-circle' : doctor.status === 'rejected' ? 'times-circle' : 'clock',
                            color: doctor.status === 'approved' ? 'success' : doctor.status === 'rejected' ? 'danger' : 'warning'
                        });
                    });
                }
                
                // Generate notifications from accounts
                if (accountsResult.data) {
                    accountsResult.data.forEach(account => {
                        notifications.push({
                            id: `account_${account.id}`,
                            type: account.status === 'approved' ? 'approval' : account.status === 'rejected' ? 'rejection' : 'system',
                            title: `Account ${account.status === 'approved' ? 'Approved' : account.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `${account.name} has been ${account.status}`,
                            created_at: account.updated_at || account.created_at,
                            read: Math.random() > 0.3,
                            icon: account.status === 'approved' ? 'check-circle' : account.status === 'rejected' ? 'times-circle' : 'clock',
                            color: account.status === 'approved' ? 'success' : account.status === 'rejected' ? 'danger' : 'warning'
                        });
                    });
                }
                
                // Generate notifications from products
                if (productsResult.data) {
                    productsResult.data.forEach(product => {
                        notifications.push({
                            id: `product_${product.id}`,
                            type: product.status === 'approved' ? 'approval' : product.status === 'rejected' ? 'rejection' : 'system',
                            title: `Product ${product.status === 'approved' ? 'Approved' : product.status === 'rejected' ? 'Rejected' : 'Submitted'}`,
                            message: `${product.name} has been ${product.status}`,
                            created_at: product.updated_at || product.created_at,
                            read: Math.random() > 0.3,
                            icon: product.status === 'approved' ? 'check-circle' : product.status === 'rejected' ? 'times-circle' : 'clock',
                            color: product.status === 'approved' ? 'success' : product.status === 'rejected' ? 'danger' : 'warning'
                        });
                    });
                }
                
                // Sort by date
                notifications.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                
                // Apply current filters
                filterNotifications();
                
            } catch (error) {
                console.error('Error loading notifications:', error);
                showToast('Error loading notifications', 'error');
                displayNotifications([]);
            }
        }
        
        // Filter notifications based on selected filters
        function filterNotifications() {
            const statusFilter = document.querySelector('input[name="statusFilter"]:checked').value;
            const typeFilter = document.querySelector('input[name="typeFilter"]:checked').value;
            
            filteredNotifications = notifications.filter(notification => {
                const statusMatch = statusFilter === 'all' || 
                    (statusFilter === 'read' && notification.read) ||
                    (statusFilter === 'unread' && !notification.read);
                
                const typeMatch = typeFilter === 'all' || notification.type === typeFilter;
                
                return statusMatch && typeMatch;
            });
            
            displayNotifications(filteredNotifications);
            updateUnreadCount();
        }
        
        // Display notifications in the UI
        function displayNotifications(notificationsToShow) {
            const container = document.getElementById('notificationsList');
            
            if (notificationsToShow.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-bell-slash fa-3x mb-3"></i>
                        <h5>No notifications found</h5>
                        <p class="mb-0">You're all caught up!</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = notificationsToShow.map(notification => `
                <div class="notification-item ${notification.read ? '' : 'unread'}" data-id="${notification.id}">
                    <div class="d-flex align-items-start p-3 border-bottom">
                        <div class="notification-icon me-3">
                            <i class="fas fa-${notification.icon} text-${notification.color}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1 ${notification.read ? '' : 'fw-bold'}">${notification.title}</h6>
                                    <p class="mb-1 text-muted">${notification.message}</p>
                                    <small class="text-muted">${formatDateTime(notification.created_at)}</small>
                                </div>
                                <div class="d-flex gap-1">
                                    ${!notification.read ? `
                                        <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                                data-id="${notification.id}" title="Mark as read">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    ` : ''}
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <button class="dropdown-item mark-read-btn" data-id="${notification.id}">
                                                    <i class="fas fa-${notification.read ? 'eye-slash' : 'eye'} me-2"></i>
                                                    Mark as ${notification.read ? 'unread' : 'read'}
                                                </button>
                                            </li>
                                            <li>
                                                <button class="dropdown-item text-danger delete-notification-btn" 
                                                        data-id="${notification.id}">
                                                    <i class="fas fa-trash me-2"></i>Delete
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
            
            // Add event listeners for mark as read buttons
            container.querySelectorAll('.mark-read-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const notificationId = e.target.closest('[data-id]').dataset.id;
                    toggleReadStatus(notificationId);
                });
            });
            
            // Add event listeners for delete buttons
            container.querySelectorAll('.delete-notification-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const notificationId = e.target.closest('[data-id]').dataset.id;
                    deleteNotification(notificationId);
                });
            });
        }
        
        // Update unread count
        function updateUnreadCount() {
            const unreadCount = notifications.filter(n => !n.read).length;
            document.getElementById('unreadCount').textContent = unreadCount;
        }
        
        // Toggle read status of a notification
        function toggleReadStatus(notificationId) {
            const notification = notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.read = !notification.read;
                filterNotifications();
                showToast(`Notification marked as ${notification.read ? 'read' : 'unread'}`, 'success');
            }
        }
        
        // Delete a notification
        function deleteNotification(notificationId) {
            notifications = notifications.filter(n => n.id !== notificationId);
            filterNotifications();
            showToast('Notification deleted', 'success');
        }
        
        // Mark all notifications as read
        function markAllAsRead() {
            notifications.forEach(notification => {
                notification.read = true;
            });
            filterNotifications();
            showToast('All notifications marked as read', 'success');
        }
    </script>
    
    <style>
        .notification-item.unread {
            background-color: rgba(102, 126, 234, 0.05);
            border-left: 4px solid #667eea;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .notification-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .notification-item:last-child .border-bottom {
            border-bottom: none !important;
        }
    </style>
</body>
</html>
