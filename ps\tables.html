<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Tables - Product Specialist</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/main.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/main_180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/main_192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/main_512.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    
    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gradient">Data Tables</h1>
                            <p class="text-muted mb-0">Browse and export system data</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Table Selection Tabs -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="tablesTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="doctors-tab" data-bs-toggle="tab" 
                                            data-bs-target="#doctors-pane" type="button" role="tab">
                                        <i class="fas fa-user-md me-2"></i>Doctors
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="accounts-tab" data-bs-toggle="tab" 
                                            data-bs-target="#accounts-pane" type="button" role="tab">
                                        <i class="fas fa-building me-2"></i>Accounts
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="products-tab" data-bs-toggle="tab" 
                                            data-bs-target="#products-pane" type="button" role="tab">
                                        <i class="fas fa-box me-2"></i>Products
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="companies-tab" data-bs-toggle="tab" 
                                            data-bs-target="#companies-pane" type="button" role="tab">
                                        <i class="fas fa-industry me-2"></i>Companies
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="tab-content" id="tablesTabContent">
                            <!-- Doctors Tab -->
                            <div class="tab-pane fade show active" id="doctors-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="mb-0">Doctors Directory</h5>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm" id="refreshDoctors">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" id="exportDoctors">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    <div id="doctorsTable">
                                        <!-- Smart table will be rendered here -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Accounts Tab -->
                            <div class="tab-pane fade" id="accounts-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="mb-0">Accounts Directory</h5>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm" id="refreshAccounts">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" id="exportAccounts">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    <div id="accountsTable">
                                        <!-- Smart table will be rendered here -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Products Tab -->
                            <div class="tab-pane fade" id="products-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="mb-0">Products Catalog</h5>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm" id="refreshProducts">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" id="exportProducts">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    <div id="productsTable">
                                        <!-- Smart table will be rendered here -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Companies Tab -->
                            <div class="tab-pane fade" id="companies-pane" role="tabpanel">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="mb-0">Companies Directory</h5>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm" id="refreshCompanies">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="btn btn-outline-success btn-sm" id="exportCompanies">
                                                <i class="fas fa-download me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    <div id="companiesTable">
                                        <!-- Smart table will be rendered here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom Scripts -->
    <script type="module">
        import { requireRole, initAuth } from '/js/auth.js';
        import { initLayout, setActiveNavItem } from '/js/layout.js';
        import { showToast } from '/js/utils.js';
        import { 
            getApprovedDoctors,
            getApprovedAccounts,
            getApprovedProducts,
            getCompanies
        } from '/js/api.js';
        import { SmartTable } from '/js/tables.js';
        
        let doctorsTable, accountsTable, productsTable, companiesTable;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            // Check authentication and role
            if (!requireRole(['product_specialist'])) return;
            
            // Initialize layout
            initAuth();
            initLayout();
            setActiveNavItem();
            
            // Initialize tables
            initializeTables();
            
            // Load initial data
            await loadDoctors();
            
            // Setup tab change handlers
            setupTabHandlers();
            
            // Setup refresh and export buttons
            setupButtons();
        });
        
        // Initialize all smart tables
        function initializeTables() {
            // Doctors table
            doctorsTable = new SmartTable('doctorsTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            doctorsTable.setColumns([
                { key: 'name', title: 'Doctor Name', sortable: true, width: '250px' },
                { key: 'specialty', title: 'Specialty', sortable: true, width: '200px' },
                { key: 'phone', title: 'Phone', sortable: false, width: '150px' },
                { 
                    key: 'accounts_count', 
                    title: 'Associated Accounts', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => value || 0
                },
                { 
                    key: 'created_at', 
                    title: 'Added Date', 
                    type: 'date', 
                    sortable: true, 
                    width: '120px' 
                }
            ]);
            
            // Accounts table
            accountsTable = new SmartTable('accountsTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            accountsTable.setColumns([
                { key: 'name', title: 'Account Name', sortable: true, width: '300px' },
                { 
                    key: 'type', 
                    title: 'Type', 
                    sortable: true, 
                    width: '100px',
                    render: (value) => `<span class="badge bg-${value === 'private' ? 'primary' : 'info'}">${value.toUpperCase()}</span>`
                },
                { key: 'governorate', title: 'Governorate', sortable: true, width: '150px' },
                { 
                    key: 'doctors_count', 
                    title: 'Associated Doctors', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => value || 0
                },
                { 
                    key: 'created_at', 
                    title: 'Added Date', 
                    type: 'date', 
                    sortable: true, 
                    width: '120px' 
                }
            ]);
            
            // Products table
            productsTable = new SmartTable('productsTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            productsTable.setColumns([
                { key: 'name', title: 'Product Name', sortable: true, width: '300px' },
                { 
                    key: 'company_name', 
                    title: 'Company', 
                    sortable: true, 
                    width: '200px',
                    render: (value, row) => row.companies?.name || 'N/A'
                },
                { 
                    key: 'category_name', 
                    title: 'Category', 
                    sortable: true, 
                    width: '150px',
                    render: (value, row) => row.categories?.name || 'N/A'
                },
                { 
                    key: 'created_at', 
                    title: 'Added Date', 
                    type: 'date', 
                    sortable: true, 
                    width: '120px' 
                }
            ]);
            
            // Companies table
            companiesTable = new SmartTable('companiesTable', {
                pageSize: 50,
                stickyHeader: true,
                infiniteScroll: true,
                exportable: true,
                filterable: true,
                columnToggle: true
            });
            
            companiesTable.setColumns([
                { key: 'name', title: 'Company Name', sortable: true, width: '400px' },
                { 
                    key: 'products_count', 
                    title: 'Products Count', 
                    sortable: true, 
                    width: '150px',
                    render: (value) => value || 0
                },
                { 
                    key: 'created_at', 
                    title: 'Added Date', 
                    type: 'date', 
                    sortable: true, 
                    width: '120px' 
                }
            ]);
        }
        
        // Setup tab change handlers
        function setupTabHandlers() {
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', async (e) => {
                    const targetId = e.target.getAttribute('data-bs-target');
                    
                    switch (targetId) {
                        case '#doctors-pane':
                            await loadDoctors();
                            break;
                        case '#accounts-pane':
                            await loadAccounts();
                            break;
                        case '#products-pane':
                            await loadProducts();
                            break;
                        case '#companies-pane':
                            await loadCompanies();
                            break;
                    }
                });
            });
        }
        
        // Setup refresh and export buttons
        function setupButtons() {
            // Doctors buttons
            document.getElementById('refreshDoctors').addEventListener('click', loadDoctors);
            document.getElementById('exportDoctors').addEventListener('click', () => {
                doctorsTable.exportToExcel(doctorsTable.filteredData, 'doctors_directory');
            });
            
            // Accounts buttons
            document.getElementById('refreshAccounts').addEventListener('click', loadAccounts);
            document.getElementById('exportAccounts').addEventListener('click', () => {
                accountsTable.exportToExcel(accountsTable.filteredData, 'accounts_directory');
            });
            
            // Products buttons
            document.getElementById('refreshProducts').addEventListener('click', loadProducts);
            document.getElementById('exportProducts').addEventListener('click', () => {
                productsTable.exportToExcel(productsTable.filteredData, 'products_catalog');
            });
            
            // Companies buttons
            document.getElementById('refreshCompanies').addEventListener('click', loadCompanies);
            document.getElementById('exportCompanies').addEventListener('click', () => {
                companiesTable.exportToExcel(companiesTable.filteredData, 'companies_directory');
            });
        }
        
        // Load doctors data
        async function loadDoctors() {
            try {
                const doctors = await getApprovedDoctors();
                doctorsTable.setData(doctors);
                showToast('Doctors data loaded', 'success');
            } catch (error) {
                console.error('Error loading doctors:', error);
                showToast('Error loading doctors', 'error');
                doctorsTable.setData([]);
            }
        }
        
        // Load accounts data
        async function loadAccounts() {
            try {
                const accounts = await getApprovedAccounts();
                accountsTable.setData(accounts);
                showToast('Accounts data loaded', 'success');
            } catch (error) {
                console.error('Error loading accounts:', error);
                showToast('Error loading accounts', 'error');
                accountsTable.setData([]);
            }
        }
        
        // Load products data
        async function loadProducts() {
            try {
                const products = await getApprovedProducts();
                productsTable.setData(products);
                showToast('Products data loaded', 'success');
            } catch (error) {
                console.error('Error loading products:', error);
                showToast('Error loading products', 'error');
                productsTable.setData([]);
            }
        }
        
        // Load companies data
        async function loadCompanies() {
            try {
                const companies = await getCompanies();
                companiesTable.setData(companies);
                showToast('Companies data loaded', 'success');
            } catch (error) {
                console.error('Error loading companies:', error);
                showToast('Error loading companies', 'error');
                companiesTable.setData([]);
            }
        }
    </script>
</body>
</html>
