#!/usr/bin/env python3
"""
Simple HTTP Server for Market Tracking System Local Development
This server serves static files and handles CORS for local development.
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # Handle routing for SPA-like behavior
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # If requesting a directory or root, serve index.html
        if path == '/' or path.endswith('/'):
            self.path = '/index.html'
        
        # If requesting a file that doesn't exist and doesn't have an extension,
        # try to serve it as an HTML file
        elif '.' not in os.path.basename(path):
            potential_html = path + '.html'
            if os.path.exists('.' + potential_html):
                self.path = potential_html
        
        return super().do_GET()

def run_server(port=8000):
    """Run the development server"""
    
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Create server
    with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
        print(f"🚀 Market Tracking System Development Server")
        print(f"📂 Serving files from: {os.getcwd()}")
        print(f"🌐 Server running at: http://localhost:{port}")
        print(f"🔗 Open in browser: http://localhost:{port}")
        print(f"⚠️  Make sure you have:")
        print(f"   - Created logo files in /assets/ directory")
        print(f"   - Run test_users.sql in your Supabase project")
        print(f"   - Updated js/config.js with your Supabase credentials")
        print(f"\n🛑 Press Ctrl+C to stop the server\n")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print(f"\n🛑 Server stopped")
            sys.exit(0)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Market Tracking System Development Server')
    parser.add_argument('--port', '-p', type=int, default=8000, 
                       help='Port to run the server on (default: 8000)')
    
    args = parser.parse_args()
    run_server(args.port)
