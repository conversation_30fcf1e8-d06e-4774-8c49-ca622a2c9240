@echo off
echo.
echo ========================================
echo  Market Tracking System - Node.js Server
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "index.html" (
    echo ERROR: index.html not found
    echo Please run this script from the project root directory
    echo.
    pause
    exit /b 1
)

REM Check if config file exists
if not exist "js\config.js" (
    echo ERROR: js/config.js not found
    echo Please make sure you have configured your Supabase credentials
    echo.
    pause
    exit /b 1
)

echo Installing http-server globally (if not already installed)...
npm install -g http-server

echo.
echo Starting Node.js development server...
echo.
echo IMPORTANT: Make sure you have:
echo  1. Created logo files in /assets/ directory (placeholders included)
echo  2. Run test_users.sql in your Supabase project  
echo  3. Updated js/config.js with your Supabase credentials
echo.
echo The server will open automatically in your browser
echo Press Ctrl+C to stop the server
echo.

REM Start the server and open browser
start "" "http://localhost:8000"
http-server -p 8000 -c-1 --cors

pause
