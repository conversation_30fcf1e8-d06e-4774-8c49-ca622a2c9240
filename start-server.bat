@echo off
echo.
echo ========================================
echo  Market Tracking System - Local Server
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "index.html" (
    echo ERROR: index.html not found
    echo Please run this script from the project root directory
    echo.
    pause
    exit /b 1
)

REM Check if config file exists
if not exist "js\config.js" (
    echo ERROR: js/config.js not found
    echo Please make sure you have configured your Supabase credentials
    echo.
    pause
    exit /b 1
)

echo Starting development server...
echo.
echo IMPORTANT: Make sure you have:
echo  1. Created logo files in /assets/ directory
echo  2. Run test_users.sql in your Supabase project  
echo  3. Updated js/config.js with your Supabase credentials
echo.
echo The server will open automatically in your browser
echo Press Ctrl+C to stop the server
echo.

REM Start the server and open browser
start "" "http://localhost:8000"
python server.py

pause
