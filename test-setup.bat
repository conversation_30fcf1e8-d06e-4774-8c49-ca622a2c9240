@echo off
echo.
echo ========================================
echo  Market Tracking System - Setup Test
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js is installed
    node --version
)

echo.

REM Check if we're in the right directory
echo [2/5] Checking project files...
if not exist "index.html" (
    echo ❌ ERROR: index.html not found
    echo Please run this script from the project root directory
    echo.
    pause
    exit /b 1
) else (
    echo ✅ index.html found
)

if not exist "js\config.js" (
    echo ❌ ERROR: js/config.js not found
    echo Please make sure you have configured your Supabase credentials
    echo.
    pause
    exit /b 1
) else (
    echo ✅ js/config.js found
)

if not exist "assets\logo.svg" (
    echo ❌ ERROR: assets/logo.svg not found
    echo Logo files are missing
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Logo files found
)

echo.

REM Install http-server if not already installed
echo [3/5] Installing http-server...
npm list -g http-server >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing http-server globally...
    npm install -g http-server
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Failed to install http-server
        echo Please check your npm permissions
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ http-server installed successfully
    )
) else (
    echo ✅ http-server already installed
)

echo.

REM Check test data
echo [4/5] Checking test data...
if not exist "test_users.sql" (
    echo ❌ ERROR: test_users.sql not found
    echo.
    pause
    exit /b 1
) else (
    echo ✅ test_users.sql found
    echo.
    echo IMPORTANT: Make sure you have run test_users.sql in your Supabase project!
    echo.
)

echo [5/5] Setup complete!
echo.
echo ========================================
echo  Ready to start the server!
echo ========================================
echo.
echo Next steps:
echo 1. Make sure you ran test_users.sql in Supabase
echo 2. Update js/config.js with your Supabase credentials
echo 3. Run: start-server-node.bat
echo 4. Open: http://localhost:8000
echo 5. Login as: admin/admin123
echo.
echo Press any key to continue...
pause >nul
