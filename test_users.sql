-- Test Users for Market Tracking System
-- Run this SQL in your Supabase SQL Editor to create test accounts
-- Note: Admin user already exists in main schema, so we skip it

-- First, get the admin employee ID for reference
-- The admin employee should already exist with ID = 1

-- Insert additional employees (hierarchical structure)
INSERT INTO employees (code_number, full_name, position_title, line, area, governorate, direct_manager_id) VALUES
-- Senior Managers (report to admin - employee_id = 1)
('SM001', '<PERSON>', 'Senior Manager', 'Orthopedic', 'North', 'Cairo', 1),
('SM002', '<PERSON><PERSON>', 'Senior Manager', 'Cardiac', 'South', 'Alexandria', 1),

-- District Managers (report to senior managers)
('DM001', '<PERSON>', 'District Manager', 'Orthopedic', 'Cairo North', 'Cairo', (SELECT id FROM employees WHERE code_number = 'SM001')),
('DM002', '<PERSON><PERSON>', 'District Manager', 'Orthopedic', 'Giza West', 'Giza', (SELECT id FROM employees WHERE code_number = 'SM001')),
('DM003', '<PERSON><PERSON><PERSON>', 'District Manager', '<PERSON><PERSON>', 'Alex Central', 'Alexandria', (SELECT id FROM employees WHERE code_number = 'SM002')),

-- Product Specialists (report to district managers)
('PS001', 'Sara Mohamed', 'Product Specialist', 'Orthopedic', 'Cairo Downtown', 'Cairo', (SELECT id FROM employees WHERE code_number = 'DM001')),
('PS002', 'Amr Tarek', 'Product Specialist', 'Orthopedic', 'Cairo East', 'Cairo', (SELECT id FROM employees WHERE code_number = 'DM001')),
('PS003', 'Mona Sayed', 'Product Specialist', 'Orthopedic', 'Giza Central', 'Giza', (SELECT id FROM employees WHERE code_number = 'DM002')),
('PS004', 'Karim Farouk', 'Product Specialist', 'Cardiac', 'Alex Marina', 'Alexandria', (SELECT id FROM employees WHERE code_number = 'DM003')),
('PS005', 'Dina Osama', 'Product Specialist', 'Cardiac', 'Alex Montaza', 'Alexandria', (SELECT id FROM employees WHERE code_number = 'DM003'));

-- Now insert users (linked to employees)
INSERT INTO users (username, password_plain, role, employee_id, is_active) VALUES
-- Senior Manager users
('sm.mohamed', 'sm123', 'senior_manager', (SELECT id FROM employees WHERE code_number = 'SM001'), true),
('sm.fatma', 'sm123', 'senior_manager', (SELECT id FROM employees WHERE code_number = 'SM002'), true),

-- District Manager users
('dm.omar', 'dm123', 'district_manager', (SELECT id FROM employees WHERE code_number = 'DM001'), true),
('dm.nour', 'dm123', 'district_manager', (SELECT id FROM employees WHERE code_number = 'DM002'), true),
('dm.youssef', 'dm123', 'district_manager', (SELECT id FROM employees WHERE code_number = 'DM003'), true),

-- Product Specialist users
('ps.sara', 'ps123', 'product_specialist', (SELECT id FROM employees WHERE code_number = 'PS001'), true),
('ps.amr', 'ps123', 'product_specialist', (SELECT id FROM employees WHERE code_number = 'PS002'), true),
('ps.mona', 'ps123', 'product_specialist', (SELECT id FROM employees WHERE code_number = 'PS003'), true),
('ps.karim', 'ps123', 'product_specialist', (SELECT id FROM employees WHERE code_number = 'PS004'), true),
('ps.dina', 'ps123', 'product_specialist', (SELECT id FROM employees WHERE code_number = 'PS005'), true);

-- Insert some basic master data for testing

-- Companies
INSERT INTO companies (name) VALUES
('Al-Riyadah Medical'),
('Johnson & Johnson'),
('Medtronic'),
('Stryker'),
('Zimmer Biomet'),
('Smith & Nephew'),
('Boston Scientific'),
('Abbott');

-- Categories
INSERT INTO categories (name) VALUES
('Orthopedic Implants'),
('Cardiac Devices'),
('Surgical Instruments'),
('Disposables'),
('Monitoring Equipment'),
('Diagnostic Tools');

-- Sample Products (approved)
INSERT INTO products (company_id, category_id, name, status) VALUES
-- Al-Riyadah Medical products
(1, 1, 'Hip Replacement System Pro', 'approved'),
(1, 1, 'Knee Joint Implant Advanced', 'approved'),
(1, 3, 'Surgical Drill Set', 'approved'),
(1, 4, 'Sterile Gauze Pack', 'approved'),

-- Johnson & Johnson products
(2, 1, 'DePuy Hip System', 'approved'),
(2, 1, 'Synthes Trauma Plates', 'approved'),
(2, 3, 'Ethicon Sutures', 'approved'),

-- Medtronic products
(3, 2, 'Pacemaker Model X1', 'approved'),
(3, 2, 'Cardiac Stent Premium', 'approved'),
(3, 5, 'Patient Monitor Pro', 'approved'),

-- Stryker products
(4, 1, 'Mako Robotic System', 'approved'),
(4, 3, 'Surgical Navigation Tools', 'approved');

-- Sample Accounts (approved)
INSERT INTO accounts (name, type, governorate, status) VALUES
-- Cairo accounts
('Cairo University Hospital', 'upa', 'Cairo', 'approved'),
('Kasr Al Ainy Hospital', 'upa', 'Cairo', 'approved'),
('As-Salam Hospital', 'private', 'Cairo', 'approved'),
('Dar Al Fouad Hospital', 'private', 'Cairo', 'approved'),
('Maadi Hospital', 'private', 'Cairo', 'approved'),

-- Alexandria accounts
('Alexandria University Hospital', 'upa', 'Alexandria', 'approved'),
('Alexandria Medical Center', 'private', 'Alexandria', 'approved'),
('Smouha Medical Center', 'private', 'Alexandria', 'approved'),

-- Giza accounts
('Giza General Hospital', 'upa', 'Giza', 'approved'),
('Sheikh Zayed Hospital', 'private', 'Giza', 'approved');

-- Sample Doctors (approved)
INSERT INTO doctors (name, phone, specialty, status) VALUES
('Dr. Ahmed Mostafa', '***********', 'Orthopedic Surgery', 'approved'),
('Dr. Mona Hassan', '***********', 'Cardiac Surgery', 'approved'),
('Dr. Khaled Farouk', '***********', 'Orthopedic Surgery', 'approved'),
('Dr. Nadia Ibrahim', '***********', 'Cardiac Surgery', 'approved'),
('Dr. Tarek Mahmoud', '***********', 'General Surgery', 'approved'),
('Dr. Laila Sayed', '***********', 'Orthopedic Surgery', 'approved'),
('Dr. Youssef Omar', '***********', 'Cardiac Surgery', 'approved'),
('Dr. Rania Khaled', '***********', 'Orthopedic Surgery', 'approved');

-- Sample Cases (for demonstration)
INSERT INTO cases (case_date, doctor_id, account_id, comments, status, created_by, created_at) VALUES
('2024-01-15', 1, 1, 'Hip replacement surgery successful', 'approved', (SELECT id FROM users WHERE username = 'ps.sara'), '2024-01-15 10:30:00'),
('2024-01-16', 2, 2, 'Cardiac procedure completed', 'approved', (SELECT id FROM users WHERE username = 'ps.karim'), '2024-01-16 14:20:00'),
('2024-01-17', 3, 3, 'Knee surgery pending review', 'pending', (SELECT id FROM users WHERE username = 'ps.amr'), '2024-01-17 09:15:00'),
('2024-01-18', 4, 4, 'Emergency cardiac case', 'approved', (SELECT id FROM users WHERE username = 'ps.dina'), '2024-01-18 16:45:00'),
('2024-01-19', 5, 5, 'Routine orthopedic procedure', 'pending', (SELECT id FROM users WHERE username = 'ps.mona'), '2024-01-19 11:30:00');

-- Sample Case Products (linking cases to products)
INSERT INTO case_products (case_id, company_id, product_id, units) VALUES
-- Case 1: Hip replacement
(1, 1, 1, 1), -- Hip Replacement System Pro
(1, 1, 4, 5), -- Sterile Gauze Pack

-- Case 2: Cardiac procedure
(2, 3, 8, 1), -- Pacemaker Model X1
(2, 3, 10, 2), -- Patient Monitor Pro

-- Case 3: Knee surgery
(3, 1, 2, 1), -- Knee Joint Implant Advanced
(3, 1, 3, 1), -- Surgical Drill Set

-- Case 4: Emergency cardiac
(4, 3, 9, 2), -- Cardiac Stent Premium

-- Case 5: Orthopedic procedure
(5, 4, 11, 1); -- Mako Robotic System

-- Create some doctor-account associations
INSERT INTO doctor_accounts (doctor_id, account_id) VALUES
(1, 1), (1, 3), (1, 4), -- Dr. Ahmed works at multiple hospitals
(2, 2), (2, 4), -- Dr. Mona at cardiac centers
(3, 1), (3, 5), -- Dr. Khaled orthopedic
(4, 2), (4, 7), -- Dr. Nadia cardiac
(5, 3), (5, 9), -- Dr. Tarek general
(6, 5), (6, 10), -- Dr. Laila orthopedic
(7, 7), (7, 8), -- Dr. Youssef cardiac in Alex
(8, 8), (8, 10); -- Dr. Rania orthopedic

-- Update sequences to avoid conflicts
SELECT setval('employees_id_seq', (SELECT MAX(id) FROM employees));
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('companies_id_seq', (SELECT MAX(id) FROM companies));
SELECT setval('categories_id_seq', (SELECT MAX(id) FROM categories));
SELECT setval('products_id_seq', (SELECT MAX(id) FROM products));
SELECT setval('accounts_id_seq', (SELECT MAX(id) FROM accounts));
SELECT setval('doctors_id_seq', (SELECT MAX(id) FROM doctors));
SELECT setval('cases_id_seq', (SELECT MAX(id) FROM cases));
SELECT setval('case_products_id_seq', (SELECT MAX(id) FROM case_products));
SELECT setval('doctor_accounts_id_seq', (SELECT MAX(id) FROM doctor_accounts));
